using System.Collections.Generic;
using QFramework;
using UnityEngine;

public class Audio2Lip : Mono<PERSON><PERSON><PERSON><PERSON>, IController {
    [Tooltip("Which lip sync provider to use for viseme computation.")]
    public OVRLipSync.ContextProviders provider = OVRLipSync.ContextProviders.Enhanced;

    [Tooltip("Enable DSP offload on supported Android devices.")]
    public bool enableAcceleration = true;

    [SerializeField] private uint Context;
    [SerializeField] private AudioSource m_AudioSource;

    /// <summary>
    ///     模型的SkinnedMeshRenderer组件
    /// </summary>
    public SkinnedMeshRenderer skinnedMeshRenderer;

    /// <summary>
    ///     blendshape权重倍数
    /// </summary>
    public float blendWeightMultiplier = 100f;

    private float[] accumulatedWeights;

    /// <summary>
    ///     音素分析结果
    /// </summary>
    protected OVRLipSync.Frame Frame { get; } = new();

    private void Awake() {
        m_AudioSource = GetComponent<AudioSource>();
        if (Context == 0)
            if (OVRLipSync.CreateContext(ref Context, provider, 0, enableAcceleration)
                != OVRLipSync.Result.Success) {
                Debug.LogError("OVRLipSyncContextBase.Start ERROR: Could not create" +
                               " Phoneme context.");
                return;
            }

        this.RegisterEvent<ChangeCharacterSucceedEvent>(e => {
            skinnedMeshRenderer = e.mCharacter.transform.RecursiveFindChild("HEAD").GetComponent<SkinnedMeshRenderer>();
        }).UnRegisterWhenGameObjectDestroyed(gameObject);
    }

    private void Update() {
        if ((Frame != null) & (skinnedMeshRenderer != null)) SetBlenderShapes();
    }

    private void OnAudioFilterRead(float[] data, int channels) {
        ProcessAudioSamplesRaw(data, channels);
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    /// <summary>
    ///     Pass F32 PCM audio buffer to the lip sync module
    /// </summary>
    /// <param name="data">Data.</param>
    /// <param name="channels">Channels.</param>
    public void ProcessAudioSamplesRaw(float[] data, int channels) {
        // Send data into Phoneme context for processing (if context is not 0)
        lock (this) {
            if (OVRLipSync.IsInitialized() != OVRLipSync.Result.Success) return;
            var frame = Frame;
            OVRLipSync.ProcessFrame(Context, data, frame, channels == 2);
        }
    }

    private void SetBlenderShapes() {
        // 初始化累积数组
        accumulatedWeights = new float[24]; // 假设有23个形态键，按实际情况调整

        // 逐个音素处理，累积形态键的影响
        for (var i = 0; i < Frame.Visemes.Length; i++) {
            var vowelName = ((OVRLipSync.Viseme)i).ToString();
            var blendWeight = blendWeightMultiplier * Frame.Visemes[i];

            // 累积每个音素的影响
            AccumulateVowelShape(vowelName, blendWeight);
        }

        // 最后一次根据累积值设置所有形态键
        for (var i = 0; i < accumulatedWeights.Length; i++)
            skinnedMeshRenderer.SetBlendShapeWeight(i, accumulatedWeights[i]);
    }

    public void AccumulateVowelShape(string vowelName, float blendWeight) {
        var vowelIndexList = new List<int> {
            0, // 嘴巴张大（下嘴唇向下）
            15, 16, // 左右向外
            17, 18, // 左右向上
            19, 20, // 左右向内
            21, // 上嘴唇向上
            22, 23 // 左右向下
        };
        var weights = new List<double>();
        switch (vowelName) {
            // case "sil": // 静音 | neutral      | (none - silence) |
            //     weights = new List<double>{ 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 };
            //     break;
            //
            // case "PP": // 双唇闭合音 | p, b, m      | put, bat, mat    |
            //     weights = new List<double>{ 0.1, 0.0, 0.0, 0.1, 0.1, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 };
            //     break;
            //
            // case "FF": // 嘴唇咧开音| f, v         | fat, vat         |
            //     weights = new List<double>{ 0.2, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.1, 0.0, 0.0, 0.0, 0.0, 0.0 };
            //     break;
            //
            // case "TH": // 舌齿音| th           | think, that      |
            //     weights = new List<double>{ 0.1, 0.0, 0.0, 0.0, 0.0, 0.3, 0.3, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 };
            //     break;
            // case "DD": // 舌尖音| t, d         | tip, doll        |
            //     weights = new List<double>{ 0.1, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.2, 0.0, 0.0, 0.0, 0.0, 0.0 };
            //     break;
            //
            // case "kk": // 舌根音| k, g         | call, gas        |
            //     weights = new List<double>{ 0.2, 0.0, 0.0, 0.0, 0.0, 0.3, 0.3, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 };
            //     break;
            //
            // case "CH": // 软腭音| tS, dZ, S    | chair, join, she |
            //     weights = new List<double>{ 0.2, 0.0, 0.0, 0.0, 0.0, 0.4, 0.4, 0.0, 0.0, 0.1, 0.0, 0.0, 0.0 };
            //     break;
            //
            // case "SS": // 齿音| s, z         | sir, zeal        |
            //     weights = new List<double>{ 0.0, 0.0, 0.0, 0.1, 0.1, 0.2, 0.2, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 };
            //     break;
            //
            // case "nn": // 鼻音| n, l         | lot, not         |
            //     weights = new List<double>{ 0.0, 0.0, 0.0, 0.1, 0.1, 0.0, 0.0, 0.1, 0.0, 0.0, 0.1, 0.1, 0.0 };
            //     break;
            //
            // case "RR": // 卷舌音| r            | red              |
            //     weights = new List<double>{ 0.0, 0.0, 0.0, 0.0, 0.0, 0.3, 0.3, 0.0, 0.0, 0.1, 0.0, 0.0, 0.0 };
            //     break;
            case "aa": // a| A:           | car              |  
                weights = new List<double> {
                    0.9,
                    0.0, 0.0,
                    0.0, 0.0,
                    0.0, 0.0,
                    0.0,
                    0.0, 0.0
                };
                break;

            case "oh": // o| oh           | toe              |  
                weights = new List<double> {
                    0.70,
                    0.0, 0.0,
                    0.0, 0.0,
                    0.40, 0.40,
                    0.0,
                    0.0, 0.0
                };
                break;

            case "E": // e| e            | bed              |  
                weights = new List<double> {
                    0.45,
                    0.33, 0.33,
                    0.15, 0.15,
                    0.2, 0.2,
                    0.0,
                    0.22, 0.22
                };
                break;

            case "ih": // i| ih           | tip              |  
                weights = new List<double> {
                    0.3,
                    0.44, 0.44,
                    0.16, 0.16,
                    0.3, 0.3,
                    0.0,
                    0.25, 0.25
                };
                break;

            case "ou": // u| ou           | book             |  
                weights = new List<double> {
                    0.33,
                    0.0, 0.0,
                    0.0, 0.0,
                    0.8, 0.8,
                    0.5,
                    0.0, 0.0
                };
                break;
            case "sil":
                weights = new List<double> {
                    0.0,
                    0.0, 0.0,
                    0.0, 0.0,
                    0.0, 0.0,
                    0.0,
                    0.0, 0.0
                };
                break;
            default:
                weights = new List<double> {
                    0.33,
                    0.0, 0.0,
                    0.0, 0.0,
                    0.8, 0.8,
                    0.5,
                    0.0, 0.0
                };
                break;
        }

        // 累加每个音素的影响到累积数组
        for (var i = 0; i < weights.Count; i++)
            accumulatedWeights[vowelIndexList[i]] += (float)weights[i] * blendWeight;
    }
}
