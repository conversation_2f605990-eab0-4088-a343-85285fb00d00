﻿using System.Collections;
using QFramework;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.UI;

public class UISceneLoadingCtrl : <PERSON>o<PERSON><PERSON><PERSON>our, IController {
    public Slider progressSlider;
    public Text progressText;
    public float minLoadingTime = 0.1f;

    private float timer;

    private void Start() {
        this.GetModel<IGameModel>().SceneLoading.Value = true;
        this.GetModel<IGameModel>().SceneLoaded.Value = false;
        progressSlider.value = 0;
        progressText.text = "0%";
        CoroutineController.manager.StartCoroutine(LoadScene());
        DontDestroyOnLoad(gameObject);
    }

    private void Update() {
        timer += Time.deltaTime;
    }


    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    private IEnumerator LoadScene() {
        this.SendCommand(new HidePageByLevelCommand(UILevelType.Main));
        this.SendCommand(new HidePageByLevelCommand(UILevelType.UIPage));
        this.GetModel<IGameModel>().SceneLoaded.Value = false;
        progressSlider.value = 0;
        progressText.text = (int)(progressSlider.value * 100) + "%";
        progressSlider.value = 0.1f;
        progressText.text = (int)(progressSlider.value * 100) + "%";
        var async = Addressables.LoadSceneAsync(this.GetModel<IGameModel>().LoadingTargetSceneID.Value);

        while (timer < minLoadingTime) {
            var maxProgress = timer / minLoadingTime;
            progressSlider.value = maxProgress;
            progressText.text = (int)(maxProgress * 100) + "%";
            yield return null;
        }

        while (!async.IsDone) {
            progressSlider.value = Mathf.Min(async.PercentComplete, async.PercentComplete);
            progressText.text = (int)(progressSlider.value * 100) + "%";
            yield return null;
        }

        // 2019加载完场景并不能直接显示
        Destroy(gameObject);
        yield return new WaitForSeconds(0.1f);
        this.GetModel<IGameModel>().SceneLoaded.Value = true;
        this.GetModel<IGameModel>().SceneLoading.Value = false;
    }
}
