﻿using QFramework;
using UnityEngine;
using UnityEngine.UI;

public class ProfileUI : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IController {
    public Button userInfoBtn;
    public Button AvatarBtn;
    public Button closeBtn;

    public GameObject userInfoUI;

    private void Start() {
        closeBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Close);
            this.SendCommand(new HidePageCommand(UIPageType.ProfileUI));
            this.SendCommand<UpdateHomepageUICommand>();
        });

        userInfoBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            userInfoUI.SetActive(true);
        });
        AvatarBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            userInfoUI.SetActive(false);
        });
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }
}
