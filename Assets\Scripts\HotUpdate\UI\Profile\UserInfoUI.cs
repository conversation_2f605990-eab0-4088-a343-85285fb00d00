﻿using QFramework;
using UnityEngine;
using UnityEngine.ResourceManagement.AsyncOperations;
using UnityEngine.UI;
using Utils;

public class UserInfoUI : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IController {
    public Image avatarImage;
    public Image vipImage;
    public InputField userNameInput;
    public Button userNameBtn;
    public Button passwordBtn;
    public InputField passwordInput;
    public InputField genderInput;
    public InputField ageInput;
    public Text avatarText;
    public Text starText;
    public Button guidanceBtn;

    private void Start() {
        userNameBtn.onClick.AddListener(() => {
            if (userNameInput.text == this.GetModel<IUserModel>().Name.Value) {
                var alertInfo = new WarningAlertInfo("与原昵称一致");
                this.SendCommand(new ShowPageCommand(UIPageType.WarningAlert, UILevelType.Alart, alertInfo));
            }
        });
        passwordBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            if (passwordInput.text == this.GetModel<IUserModel>().Password.Value) {
                var alertInfo = new WarningAlertInfo("与原密码一致");
                this.SendCommand(new ShowPageCommand(UIPageType.WarningAlert, UILevelType.Alart, alertInfo));
            } else if (passwordInput.text.Length < 6) {
                var alertInfo = new WarningAlertInfo("密码长度必须大于6个字符");
                this.SendCommand(new ShowPageCommand(UIPageType.WarningAlert, UILevelType.Alart, alertInfo));
            } else {
                this.SendCommand(new ChangePasswordCommand(passwordInput.text));
            }
        });

        guidanceBtn.onClick.AddListener(() => {
            this.GetModel<IUserModel>().IsCompleteGuidance.Value = false;
            var alertInfo = new WarningAlertInfo("重新进入，即可重启新手教程");
            this.SendCommand(new ShowPageCommand(UIPageType.WarningAlert, UILevelType.Alart, alertInfo));
        });
    }

    private async void OnEnable() {
        var userModel = this.GetModel<IUserModel>();

        var data = await this.GetSystem<INetworkSystem>().GetUserInfo(userModel.Uid.Value);
        if (data == null) return;
        userModel.SetUserInfoPart(data);
        // vipImage.gameObject.SetActiveFast(false);  // 默认值
        userNameInput.text = userModel.Name.Value; // 这里是用户的姓名，而不是用户名，变量名目前错误，先这样
        genderInput.text = userModel.Gender.Value ? "男" : "女";
        ageInput.text = userModel.Age.Value.ToString();
        starText.text = userModel.Star.ToString();
        // avatarText.text = userModel.AvatarNum.ToString();
        passwordInput.text = userModel.Password.Value;

        var obj = await this.GetSystem<IAddressableSystem>()
            .LoadAssetAsync<Sprite>(Util.GetAvatarUrl(this.GetModel<IUserModel>().Aid.Value));
        if (obj.Status == AsyncOperationStatus.Succeeded)
            avatarImage.sprite = Instantiate(obj.Result, transform, false);
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }
}
