using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using QFramework;
using UnityEngine;
using UnityEngine.UI;
using Utils;

public class MultiSelectPrefsManager : MonoBeh<PERSON>our, IController {
    public RectTransform contentPanel; // ScrollRect 的内容面板
    public Toggle toggleTemplate; // Toggle 模板
    public List<string> selectedOptions = new(); // 选中的选项
    public Button clearBtn;
    public Button closeBtn;

    private readonly List<string> allOptions = new() {
        PrefKeys.CalibrationEEGDataFilePath,
        PrefKeys.ConnectedEEGDeviceAddress,
        PrefKeys.LastResetTime,
        PrefKeys.isCorrectedEEG,
        PrefKeys.Aid
    };

    private void Awake() {
        clearBtn.onClick.AddListener(OnClearBtnClick);
        closeBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            gameObject.SetActiveFast(false);
        });
    }

    private void Start() {
        // 使用反射自动获取所有 public const string 属性
        // allOptions = GetConstantsFromStaticClass(typeof(PrefKeys)).ToList();

        // 初始化多选列表
        InitializeMultiSelectList();
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    private void InitializeMultiSelectList() {
        foreach (var option in allOptions) {
            // 创建一个新的 Toggle
            var newToggle = Instantiate(toggleTemplate, contentPanel);
            newToggle.name = option;
            newToggle.isOn = false; // 默认不选中
            newToggle.GetComponentInChildren<Text>().text = option; // 设置文本

            // 添加事件监听
            newToggle.onValueChanged.AddListener(value => {
                if (value)
                    // 如果选中，添加到选中列表
                    selectedOptions.Add(option);
                else
                    // 如果取消选中，从选中列表中移除
                    selectedOptions.Remove(option);
            });
            newToggle.gameObject.SetActiveFast(true);
        }

        // 禁用模板
        toggleTemplate.gameObject.SetActive(false);
    }

    public void PrintSelectedOptions() {
        // 打印选中的选项
        Debug.Log("选中的选项：");
        foreach (var option in selectedOptions) Debug.Log(option);
    }

    private void OnClearBtnClick() {
        this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
        foreach (var option in selectedOptions) PlayerPrefs.DeleteKey(option);
    }

    // 使用反射获取静态类中的所有 public const string 属性
    private IEnumerable<string> GetConstantsFromStaticClass(Type staticClassType) {
        return staticClassType.GetFields(BindingFlags.Public | BindingFlags.Static | BindingFlags.GetField)
            .Where(field => field.IsLiteral && !field.IsInitOnly && field.FieldType == typeof(string))
            .Select(field => (string)field.GetValue(null));
    }
}
