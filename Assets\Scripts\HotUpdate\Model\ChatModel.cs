using System;
using System.Collections.Generic;
using System.Linq;
using JetBrains.Annotations;
using QFramework;
using UnityEngine;

// 一次任务保存的总的数据
public class ArchiveInfo {
    public UserInfo UserDic { get; set; }
    public TaskInfo TaskDic { get; set; }
    public string StartTime { get; set; }
    public List<(string, float)> EventTimeStamp { get; set; }
    public string DeviceInfo { get; set; }
    public string CalibrationEEGDataFilePath { get; set; }
    public List<HistoryData> TextHistory { get; set; }
}

public class HistoryData {
    // [JsonConstructor]
    public HistoryData(string role, [CanBeNull] string inputType = null, [CanBeNull] string content = null,
        float? startTime = null, float? endTime = null, float? reactTime = null, float? timeLong = null) {
        Role = role;
        InputType = inputType;
        Content = content;
        StartTime = startTime;
        EndTime = endTime;
        ReactTime = reactTime;
        TimeLong = timeLong;
    }

    public string Role { get; set; }
    [CanBeNull] public string Content { get; set; }
    public float? StartTime { get; set; } // 开始说话时间 （相对于每次主题对话开始时间）
    public float? EndTime { get; set; } // 结束说话时间 （相对于每次主题对话开始时间）
    public float? ReactTime { get; set; } // 反应时长
    public float? TimeLong { get; set; } // 说话时长 但是，比如AI说话时长，这里保存的是语音的时长，如果有暂停，开始时间减结束时间的时长会比这个时长大
    [CanBeNull] public string InputType { get; set; } // 输入模式
}


// 对话中的各种状态
public enum ChatStates {
    Idle,
    Connecting,
    Listening,
    Speaking,
    // Vad, // 自由对话输入
    // UserSpeak,
    // UserRecord, // 录音输入
    // UserText, // 文本输入
    End,
    Start,
    Pause,
    Resume
}

public enum ChatType {
    VoiceInput,
    TextInput,
    RecordInput
}

public enum ChatOverStates {
    Normal, // 正常退出状态
    Error, // 非正常退出状态
}

public interface IChatModel : IModel {
    // 每个主题聊天开始时时间戳
    public float OneChatStartTimestamp { get; set; }
    public BindableProperty<ArchiveInfo> ArchiveInfoDic { get; set; }
    public BindableProperty<string> FileName { get; set; }
    public BindableProperty<string> RoleSystem { get; set; } //= "developer";

    public BindableProperty<ChatStates> State { get; set; } // = State.start;

    public bool IsTimeout { get; set; } //= true;
    public bool IsQuit { get; set; } //= true;

    public BindableProperty<bool> Pause { get; set; } //= false;
    public HistoryData HistoryData { get; set; }
    public List<HistoryData> m_HistoryDataList { get; set; } // 保存聊天记录及时间
    public BindableProperty<ChatType> chatType { get; set; }
    public BindableProperty<int> AudioVisualStepCount { get; set; }
    public string GetChatHistory();
    public float lastUserSpeakEndTime { get; set; }  // 上次用户停止说话时间
}

public class ChatModel : AbstractModel, IChatModel {
    // 每个主题聊天开始时时间戳
    public float OneChatStartTimestamp { get; set; } = new();
    public BindableProperty<ArchiveInfo> ArchiveInfoDic { get; set; } = new();
    public BindableProperty<string> FileName { get; set; } = new();
    public BindableProperty<string> RoleSystem { get; set; } = new();

    public BindableProperty<ChatStates> State { get; set; } = new();
    public bool IsTimeout { get; set; } = true;
    public bool IsQuit { get; set; } = false;
    public BindableProperty<bool> Pause { get; set; } = new();
    public HistoryData HistoryData { get; set; }
    public List<HistoryData> m_HistoryDataList { get; set; } = new();
    public BindableProperty<ChatType> chatType { get; set; } = new();
    public BindableProperty<int> AudioVisualStepCount { get; set; } = new();
    public float lastUserSpeakEndTime { get; set; } = 0.0f;

    public string GetChatHistory() {
        return string.Join("\n", m_HistoryDataList.Where(data => data.Role == "user").Select(data => data.Content));
    }
    protected override void OnInit() {
        RoleSystem.Value = "system";
        Pause.Value = false;
        ArchiveInfoDic.Value = new ArchiveInfo();

        chatType.Value = (ChatType)PlayerPrefs.GetInt(PrefKeys.chatType);
        // 补丁，保证不会是 ChatType.TextInput
        // if (chatType.Value == ChatType.TextInput) chatType.Value = ChatType.VoiceInput;
        // 补丁，保证是 ChatType.RecordInput 【因bug：初始时不是录音按钮时，RecordButton Awake不会被触发】
        // TODO: 解决方法，切换到RecordButton时，判断其是否已经启动过，没有的话，再发送一次虚拟人说完话事件 x
        chatType.Value = ChatType.RecordInput;
        // 保证 WindowsEditor 中是 ChatType.TextInput(便于测试)，如果是 VoiceInput，编辑器会卡住。
        if (Application.platform == RuntimePlatform.WindowsEditor)
            // 2025年3月4日 修复：本人有了麦克风，更新此处，默认是 RecordInput 模式
            chatType.Value = Microphone.devices.Length == 0 ? ChatType.TextInput : ChatType.RecordInput;
        chatType.Register(v =>
            PlayerPrefs.SetInt(PrefKeys.chatType, (int)v)
        );

        AudioVisualStepCount.Value = PlayerPrefs.GetInt(PrefKeys.AudioVisualStepCount);
        AudioVisualStepCount.Register(v =>
            PlayerPrefs.SetInt(PrefKeys.AudioVisualStepCount, v)
        );
        if (AudioVisualStepCount.Value == 0) AudioVisualStepCount.Value = 20;
    }
}
