﻿using QFramework;

public class EnterGameState : AbstractState<LaunchStates, Launch>, IController {
    public EnterGameState(FSM<LaunchStates> fsm, Launch target) : base(fsm, target) {
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    protected override async void OnEnter() {
        await this.GetSystem<IDataParseSystem>().ParseCharacterData();

        // 实现自动登录
        if (this.GetModel<IUserModel>().RememberPassword.Value == 1)
            this.SendCommand(new LoginCommand(this.GetModel<IUserModel>().UserName.Value,
                this.GetModel<IUserModel>().Password.Value, true, true));
        else
            this.SendCommand(new ShowPageCommand(UIPageType.LoginAtisUI));
    }
}
