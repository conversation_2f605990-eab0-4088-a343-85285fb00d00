using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using QFramework;
using UnityEngine;
using UnityEngine.UI;

public class AudioVisualDemo : Mono<PERSON>ehaviour, IController {
    public List<Transform> cubes;
    public Button closeBtn;
    public Slider speechRateSlider;
    public Text speechSpeedText;

    private readonly Color _greenColor = new(95 / 255f, 222 / 255f, 50 / 255f, 1f);
    private readonly Color _redColor = new(181 / 255f, 69 / 255f, 69 / 255f, 1f);
    private bool isAudioVisual;
    private int lastPosition;

    private IChatModel m_ChatModel;
    private AudioClip micRecord;
    private string microphoneDevice;
    private IVadSystem vadSystem;

    public void Awake() {
        m_ChatModel = this.GetModel<IChatModel>();
        vadSystem = this.GetSystem<IVadSystem>();
        for (var i = 0; i < transform.childCount; i++) cubes.Add(transform.GetChild(i));

        speechRateSlider.onValueChanged.AddListener(v => {
            var value = Mathf.Clamp(v, 20, 80);

            speechRateSlider.value = value;

            speechSpeedText.text = value.ToString(CultureInfo.InvariantCulture);
            m_ChatModel.AudioVisualStepCount.Value = (int)value;
        });

        closeBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Close);
            isAudioVisual = false;
            if (Microphone.IsRecording(microphoneDevice)) {
                Microphone.End(microphoneDevice);
                Debug.Log("录音结束");
            }
        });
    }

    public void OnEnable() {
        Debug.Log("AudioVisualDemo: OnEnable");
        speechRateSlider.value = m_ChatModel.AudioVisualStepCount.Value;
        if (Microphone.devices.Length > 0) {
            microphoneDevice = Microphone.devices[0];
            if (!Microphone.IsRecording(microphoneDevice)) {
                micRecord = Microphone.Start(microphoneDevice, true, 180, 16000);
                lastPosition = Microphone.GetPosition(microphoneDevice);
                isAudioVisual = true;
                StartCoroutine(AudioVisual());
            }
        }
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    private IEnumerator AudioVisual() {
        while (isAudioVisual) {
            var currentPos = Microphone.GetPosition(microphoneDevice);

            var samplesToRead = currentPos - lastPosition;
            if (samplesToRead > 0) {
                var samples = new float[samplesToRead * micRecord.channels];
                micRecord.GetData(samples, lastPosition);

                // 转换为字节数据
                var pcmData = WavUtility.ConvertToPCM(samples);
                var hasSpeech = vadSystem.HasSpeech(pcmData);

                var volumeData = new float[128];
                var offset = Microphone.GetPosition(microphoneDevice) - 128 + 1;
                if (offset < 0) yield return null;
                micRecord.GetData(volumeData, offset);
                for (var i = 0; i < cubes.Count; i++) {
                    var spectrumAvg = Mathf.Log10(Mathf.Abs(volumeData[i * 4]) + 1);
                    var targetHeight =
                        Mathf.Clamp(spectrumAvg * m_ChatModel.AudioVisualStepCount.Value, 0, 5f); // 限制高度范围
                    var smoothHeight = Mathf.Lerp(cubes[i].localScale.y, targetHeight, 0.5f); // 平滑
                    cubes[i].transform.localScale =
                        new Vector3(cubes[i].localScale.x, smoothHeight, cubes[i].localScale.z); //将可视化的物体和音波相关联
                    cubes[i].GetComponent<Image>().color = hasSpeech ? _greenColor : _redColor;
                }

                lastPosition = currentPos;
            }

            yield return new WaitForSeconds(0.1f);
        }
    }
}
