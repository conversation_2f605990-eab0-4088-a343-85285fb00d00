using QFramework;
using UnityEngine;
using UnityEngine.UI;
using Utils;

public class HotFix : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IController {
    public Button closeBtn;
    public Text text;

    private async void Awake() {
        var hotFixLog = await this.GetSystem<IDataParseSystem>().ParseHotFixLogData();
        text.text = hotFixLog;
    }

    private void Start() {
        closeBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Close);
            gameObject.SetActiveFast(false);
        });
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }
}
