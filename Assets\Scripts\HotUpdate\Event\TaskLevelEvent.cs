using System;

public class TaskPauseResumeEvent {
    public bool pause;

    public TaskPauseResumeEvent(bool pause) {
        this.pause = pause;
    }
}

public class TaskQuitEvent {
}

public class TaskStartEvent {
    public int chatTime;
    public string taskName;

    public TaskStartEvent(string taskName, int chatTime) {
        this.chatTime = chatTime;
        this.taskName = taskName;
    }
}

public class ExitTaskEvent {
}

public class UserTextEvent {
}

public class AvatarTextEvent {
}

public class AvatarSpeakEndEvent {
}

public class ChatOverEvent {
    public ChatOverStates m_chatOverStates;

    public ChatOverEvent(ChatOverStates chatOverStates) {
        m_chatOverStates = chatOverStates;
    }
}

public class ChatTimeOutEvent {
}

public class ForceStopTaskCoroutineEvent {
}

public class UserSpeakingEvent {
    public string m_text;

    public UserSpeakingEvent(string text) {
        m_text = text;
    }
}
