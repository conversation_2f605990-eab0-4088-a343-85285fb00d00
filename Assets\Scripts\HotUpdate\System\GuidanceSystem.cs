﻿using QFramework;
using UnityEngine;

public enum GuidanceCanvasType {
    UIController = 0,
    Test
}

public interface IGuidanceSystem : ISystem {
    public Canvas UIControllerCanvas { get; set; }

    public Canvas TestCanvas { get; set; }

    public Canvas GetTargetCanvas(GuidanceCanvasType canvasType);
}

public class GuidanceSystem : AbstractSystem, IGuidanceSystem {
    public Canvas UIControllerCanvas { get; set; }
    public Canvas TestCanvas { get; set; }

    public Canvas GetTargetCanvas(GuidanceCanvasType canvasType) {
        if (canvasType == GuidanceCanvasType.UIController) return UIControllerCanvas;

        if (canvasType == GuidanceCanvasType.Test) return TestCanvas;

        return null;
    }

    protected override void OnInit() {
    }
}
