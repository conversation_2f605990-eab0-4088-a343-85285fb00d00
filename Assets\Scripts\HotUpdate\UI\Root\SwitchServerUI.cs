﻿using Builtin.Utility;
using QFramework;
using UnityEngine;
using UnityEngine.UI;
using Utils;

public class SwitchServerUI : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IController {
    public Toggle toggleDev;
    public Toggle toggleTest;
    public Toggle toggleRelease;
    public Button okBtn;
    public Button cancel;
    public Button closeBtn;
    public Button testBtn;
    public ToggleGroup toggleGroup;

    private void Awake() {
        Debug.Log("SwitchServerUI Start");

        testBtn.onClick.AddListener(() => {
            var serverTypeAot = GetSelectedServerTypeAot();

            HttpHelper.Instance.Get(UtilAot.GetServerBaseUrl(serverTypeAot),
                () => {
                    var alertInfo = new WarningAlertInfo($"测试连接成功：{serverTypeAot}");
                    this.SendCommand(new ShowPageCommand(UIPageType.WarningAlert, UILevelType.Alart, alertInfo));
                },
                e => {
                    var alertInfo = new WarningAlertInfo($"测试连接失败：{serverTypeAot}");
                    this.SendCommand(new ShowPageCommand(UIPageType.WarningAlert, UILevelType.Alart, alertInfo));
                });
        });

        closeBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            gameObject.SetActiveFast(false);
        });

        cancel.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            gameObject.SetActiveFast(false);
        });

        okBtn.onClick.AddListener(() => {
            var serverTypeAot = GetSelectedServerTypeAot();
            HttpHelper.Instance.Get(UtilAot.GetServerBaseUrl(serverTypeAot),
                () => {
                    var alertInfo = new WarningAlertInfo($"连接成功：{serverTypeAot}\n重启后生效");
                    this.SendCommand(new ShowPageCommand(UIPageType.WarningAlert, UILevelType.Alart, alertInfo));
                    PlayerPrefs.SetInt(PlayerPrefKeys.ServerType, (int)serverTypeAot);
                    Debug.Log("SwitchServerUI End: " + (ServerTypeAot)PlayerPrefs.GetInt(PlayerPrefKeys.ServerType));
                    Util.AppQuit();
                },
                e => {
                    var alertInfo = new WarningAlertInfo($"连接失败：{serverTypeAot}");
                    this.SendCommand(new ShowPageCommand(UIPageType.WarningAlert, UILevelType.Alart, alertInfo));
                });
        });
    }

    private void OnEnable() {
        Debug.Log("SwitchServerUI OnEnable: " + (ServerTypeAot)PlayerPrefs.GetInt(PlayerPrefKeys.ServerType));
        switch ((ServerTypeAot)PlayerPrefs.GetInt(PlayerPrefKeys.ServerType)) {
            case ServerTypeAot.Release:
                toggleRelease.isOn = true;
                toggleTest.isOn = false;
                toggleDev.isOn = false;
                break;
            case ServerTypeAot.Test:
                toggleRelease.isOn = false;
                toggleTest.isOn = true;
                toggleDev.isOn = false;
                break;
            case ServerTypeAot.Dev:
                toggleRelease.isOn = false;
                toggleTest.isOn = false;
                toggleDev.isOn = true;
                break;
        }
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    private ServerTypeAot GetSelectedServerTypeAot() {
        var serverTypeAot = ServerTypeAot.Dev;
        if (toggleRelease.isOn) serverTypeAot = ServerTypeAot.Release;
        if (toggleTest.isOn) serverTypeAot = ServerTypeAot.Test;
        if (toggleDev.isOn) serverTypeAot = ServerTypeAot.Dev;

        return serverTypeAot;
    }
}
