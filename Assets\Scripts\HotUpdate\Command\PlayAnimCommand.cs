using QFramework;

public class PlayAnimCommand : AbstractCommand {
    private readonly int mMsgLen;

    public PlayAnimCommand(int msgLen) {
        mMsgLen = msgLen;
    }

    protected override void OnExecute() {
        this.SendEvent(new PlayAnimEvent(mMsgLen));
    }
}

public class AvatarFeedbackCommand : AbstractCommand {
    private readonly int m_state;

    public AvatarFeedbackCommand(int state) {
        m_state = state;
    }

    protected override void OnExecute() {
        this.SendEvent(new AvatarFeedbackEvent(m_state));
    }
}
