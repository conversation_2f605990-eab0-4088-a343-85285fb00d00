using Cysharp.Threading.Tasks;
using QFramework;
using UnityEngine;
using UnityEngine.ResourceManagement.AsyncOperations;
using UnityEngine.UI;
using Utils;
using Random = UnityEngine.Random;

public class AnimReward : Mono<PERSON>ehaviour, IController {
    private void Start() {
        this.RegisterEvent<PlayAnimEvent>(PlayAnim).UnRegisterWhenGameObjectDestroyed(gameObject);
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    private void PlayAnim(PlayAnimEvent e) {
        var balloonNums = e.mMsgLen * 2;
        for (var i = 0; i < balloonNums; i++) PlayAnimUniTask().Forget();
    }

    private async UniTaskVoid PlayAnimUniTask() {
        await UniTask.Delay(Mathf.RoundToInt(Random.Range(0.2f, 0.8f) * 1000)); // 转换为毫秒

        GameObject anim = null;
        var obj = this.GetSystem<IAddressableSystem>().LoadAsset<GameObject>(Util.BalloonAnimUrl);
        await obj; // 等待资源加载完成

        if (obj.Status == AsyncOperationStatus.Succeeded) {
            anim = Instantiate(obj.Result, gameObject.transform);
            anim.transform.localPosition = new Vector3(Random.Range(-800f, 800f), 0, 0);

            var spriteObj = this.GetSystem<IAddressableSystem>().LoadAsset<Sprite>(GetBollPath());
            await spriteObj; // 等待 Sprite 加载完成

            if (spriteObj.Status == AsyncOperationStatus.Succeeded)
                anim.GetComponent<Image>().sprite = spriteObj.Result;
        } else {
            Debug.LogError("Load anim.prefab Page Failed");
        }

        await UniTask.Delay(5000); // 转换为毫秒

        Destroy(anim);
    }

    private string GetBollPath() {
        return Util.BalloonRootPath + (BalloonEnum)Random.Range(0, 9) + ".png";
    }
}

internal enum BalloonEnum {
    BalloonBlue = 0,
    BalloonGreen,
    BalloonHeart,
    BalloonPinkBunny,
    BalloonPink,
    BalloonPurple,
    BalloonRed,
    BalloonYellow,
    Balloons
}
