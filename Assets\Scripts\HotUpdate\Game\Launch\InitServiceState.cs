using QFramework;

public class InitServiceState : AbstractState<LaunchStates, Launch>, IController {
    public InitServiceState(FSM<LaunchStates> fsm, Launch target) : base(fsm, target) {
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    protected override void OnEnter() {
        this.SendCommand(new InitWebCaptureCommand(ChangeState));
    }

    private void ChangeState() {
        mFSM.ChangeState(LaunchStates.InitGameConfig);
    }

    protected override void OnExit() {
    }
}
