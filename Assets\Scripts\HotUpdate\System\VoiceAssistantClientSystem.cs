//=====================================================
// - Description:   高级语音助手客户端系统
//=====================================================

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using Best.WebSockets.Implementations;
using Cysharp.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using QFramework;
using UnityEngine;
using Utils;
using XiaozhiAI.Models.IoT;
using XiaozhiAI.Models.IoT.Things;
using XiaozhiAI.Services.Mqtt;
using Debug = UnityEngine.Debug;
// using Camera = XiaozhiAI.Models.IoT.Things.Camera;
#if PLATFORM_ANDROID
using UnityEngine.Android;
#endif

public interface IVoiceAssistantClientSystem : ISystem {
    public Action<bool, bool, float[]> OnGetSpectrumData { get; set; }
    public UniTask WebSocket_IOT_Mqtt_Microphone_InIt(string url, ChatUI chaUI);
    public void StartButtonRecording();
    public void StopButtonRecording();
    public void SwitchChatType(ChatType chatType);
}

public class VoiceAssistantClientSystem : AbstractSystem, IVoiceAssistantClientSystem {
    private IAudioManagerSystem _audioManager;
    private IWebSocketSystem _wsManager;
    private IVadSystem _vadSystem;
    private IChatModel _chatModel;
    private ITaskModel _taskModel;
    private IUserModel _userModel;
    public Action<bool, bool, float[]> OnGetSpectrumData { get; set; }

    private string _url = string.Empty;

    private ChatUI _chatUI;
    private const string UserNoVoiceText = "儿童无响应";
    private const string ASDLLM = "ASDLLM";

    protected override void OnInit() {
        _audioManager = this.GetSystem<IAudioManagerSystem>();
        _wsManager = this.GetSystem<IWebSocketSystem>();
        _chatModel = this.GetModel<IChatModel>();
        _taskModel = this.GetModel<ITaskModel>();
        _userModel = this.GetModel<IUserModel>();
        _vadSystem = this.GetSystem<IVadSystem>();

        this.RegisterEvent<ChatOverEvent>(ChatOver);
        this.RegisterEvent<UserSpeakEndEvent>(OnUserSpeakEnd);
        this.RegisterEvent<InitConfigDataEvent>(OnInitConfigData);
    }

    private void OnInitConfigData(InitConfigDataEvent e) {
        vadThreshold = this.GetModel<IConfigModel>().Voice.VadThreshold; // 默认阈值，可能需要根据实际环境调整
    }

    public async UniTask WebSocket_IOT_Mqtt_Microphone_InIt(string url, ChatUI chaUI) {
        try {
            Debug.Log("开始AI初始化");

            _chatUI = chaUI;
            _chatUI.SetText();

            isPlaying =  true;
            isTtsStarted = false;
            _audioManager.ResetPlayback();

            // 初始化物联网管理器
            thingManager = ThingManager.GetInstance();
            if (thingManager == null) {
                Debug.LogError("物联网管理器初始化失败");
                return;
            }

            // 连接WebSocket
            ConnectWebSocket(url);

            // 初始化MQTT服务
            await StartMqtt();

            Debug.Log("AI初始化完成");
        } catch (Exception ex) {
            Debug.LogError($"AI初始化失败: {ex.Message}\n{ex.StackTrace}");
        }
    }

    private async UniTask StartMqtt() {
        try {
            // 创建服务实例
            mqttService = new MqttService();
            if (mqttService == null) {
                Debug.LogError("MQTT服务创建失败");
                return;
            }

            var mqttHandlerManager = new MqttMessageHandlerManager(mqttService);
            // 注册MQTT消息处理器
            mqttHandlerManager.RegisterHandler(new DefaultMqttMessageHandler());

            // 连接到MQTT服务器
            await mqttService.ConnectAsync();
            Debug.Log("MQTT服务已启动");
        } catch (Exception ex) {
            Debug.LogError($"MQTT服务启动失败: {ex.Message}\n{ex.StackTrace}");
        }
    }

    private void InitializeIotDevices() {
        try {
            if (thingManager == null) {
                Debug.LogError("物联网管理器未初始化");
                return;
            }

            // 发送设备描述
            if (!string.IsNullOrEmpty(sessionId)) {
                // 添加设备
                thingManager.ID = sessionId;
                // thingManager.AddThing(new Lamp());
                // thingManager.AddThing(new Speaker());
                // thingManager.AddThing(new Camera());
                // thingManager.AddThing(new DuoJi());
                // thingManager.AddThing(new Screenctr());
                // thingManager.AddThing(new OpenApp());
                Debug.Log("发送IoT设备描述");
                SendIotDescriptors();
            } else {
                Debug.LogWarning("Session ID为空，跳过物联网设备初始化");
            }
        } catch (Exception ex) {
            Debug.LogError($"物联网设备初始化失败: {ex.Message}\n{ex.StackTrace}");
        }
    }


    private void ConnectWebSocket(string url = null) {
        Debug.Log("ConnectWebSocket");
        if (string.IsNullOrEmpty(_url)) _url = url;
        _wsManager.Connect(_url);
        _wsManager.OnOpen += OnOpen;
        _wsManager.OnMessage += OnMessage;
        _wsManager.OnClose += OnClose;
    }

    public void SendDectMessage(string msg) {
        var helloMsg = new {
            type = "listen",
            state = "detect",
            text = msg
        };
        var json = JsonConvert.SerializeObject(helloMsg);
        _wsManager.SendMessage(json);
    }

    private void OnOpen() {
        var helloMsg = new {
            type = "hello",
            version = 1,
            transport = "websocket",
            audio_params = new {
                format = "opus",
                sample_rate = 16000, // 更新为录音采样率
                channels = 1,
                frame_duration = 60 // 更新为60ms帧大小
            }
        };

        var json = JsonConvert.SerializeObject(helloMsg);
        _wsManager.SendMessage(json);
    }

    private void OnMessage(string message) {
        if (string.IsNullOrEmpty(message)) {
            Debug.LogWarning("收到空消息");
            return;
        }

        // Debug.LogWarning("msg:::" + message);
        try {
            var msg = JObject.Parse(message);

            var msgType = msg["type"]?.ToString();
            if (string.IsNullOrEmpty(msgType)) {
                Debug.LogError("消息类型为空");
                return;
            }

            switch (msgType)
            {
                case "hello":
                {
                    sessionId = $"{_userModel.UserName.Value}_{_taskModel.taskInfo.name}_{Util.NowTime()}";
                    // 初始化物联网设备
                    InitializeIotDevices();

                    SendChatConfigMessage();
                    break;
                }
                case "atis_update_config":
                {
                    var status = msg["status"]?.ToString();
                    if (status == "success") {
                        _taskModel.taskInfo.llmName = msg["llm"]?.ToString();
                        // 开始音频处理
                        SendAudioCoroutine().ToCoroutine();
                    } else {
                        // TODO: 处理配置失败, 客户端发送对应音频，让联系管理员，并退出APP
                        Debug.LogError("配置更新失败！");
                        ServerError();
                    }

                    break;
                }
                case "llm":
                {
                    var emotion = msg["emotion"]?.ToString();
                    var text = msg["text"]?.ToString();
                    if (!string.IsNullOrEmpty(emotion)) {
                        // Debug.Log($"[AI emotion]:【{emotion}】【{text}】");
                        this.SendEvent(new AvatarEmojiEvent(emotion));
                    }

                    break;
                }
                case "stt":
                {
                    var sttState = msg["state"]?.ToString();
                    switch (sttState)
                    {
                        case "start":
                            // 开始说话
                            isSpeaking = true;
                            startSpeakTimestamp = Time.time;
                            Debug.Log("StartSpeaking");
                            break;
                        case "stop": {
                            // 用户说完话：由服务端再次明确
                            // 当客户端vad没有识别出用户说话时，客户端会没有发送 listen-stop 消息，此处需要设置一下
                            Debug.Log("StopSpeaking");
                            isSpeaking = false;
                            isListening = false;
                            stopSpeakTimestamp = Time.time;

                            listenState = "stop";
                            this.SendEvent<UserSpeakEndEvent>();

                            break;
                        }
                    }

                    var text = msg["text"]?.ToString();
                    if (!string.IsNullOrEmpty(text)) {
                        Debug.Log("[用户]:" + text);
                        _chatUI.SetText("[用户]:" + text);

                        startSpeakTimestamp = startSpeakTimestamp != 0? startSpeakTimestamp : Time.time;
                        var startTime = startSpeakTimestamp - _chatModel.OneChatStartTimestamp;
                        stopSpeakTimestamp = stopSpeakTimestamp != 0 ? stopSpeakTimestamp : Time.time;
                        var endTime = Time.time - _chatModel.OneChatStartTimestamp;
                        var reactTime = startSpeakTimestamp - startWaitingTimestamp;
                        var timeLong = endTime - startTime;
                        var historyData = new HistoryData(
                            "user",
                            _chatModel.chatType.Value.ToString(),
                            text,
                            startTime,
                            endTime,
                            reactTime,
                            timeLong);
                        _chatModel.m_HistoryDataList.Add(historyData);

                        stopSpeakTimestamp = 0;
                        startSpeakTimestamp = 0;
                        startWaitingTimestamp = 0;

                        if (text != UserNoVoiceText) {
                            this.SendEvent(new PlayAnimEvent(text.Length));
                            _chatModel.lastUserSpeakEndTime = Time.time;
                            // TODO： 统计轮次等
                            _taskModel.taskMetrics.totalSentenceLength += text.Length;
                            _taskModel.taskMetrics.dialogueTurns++;
                            _taskModel.taskMetrics.totalThinkingTime += reactTime;
                            _taskModel.taskMetrics.maxThinkingInterval = Math.Max(_taskModel.taskMetrics.maxThinkingInterval, reactTime);
                        }

                        this.SendEvent<UserTextEvent>();
                    }

                    break;
                }
                case "tts":
                {
                    var state = msg["state"]?.ToString();
                    if (string.IsNullOrEmpty(state)) {
                        Debug.LogError("tts状态为空");
                        return;
                    }

                    ttsState = state;
                    if (state == "start") {
                        // stt 结束
                        // 没有用的消息，或者是服务端的问题，发送时间不正确
                        if (_audioManager is { isRecording: true }) {
                            isPlaying = true;
                        }
                    } else if (state == "sentence_start") {
                        if (_audioManager is { isRecording: true }) {
                            isPlaying = true;
                        }

                        if (!isTtsStarted) {
                            _chatUI.SetText("[AI]:");
                            // Debug.Log("正在说话中...");
                            _chatUI.SetTipsText("正在说话中...");
                            this.SendEvent(new AvatarFeedbackEvent(2));
                            isTtsStarted = true;
                            var historyData = new HistoryData(
                                "assistant",
                                _chatModel.chatType.Value.ToString(),
                                "");
                            _chatModel.m_HistoryDataList.Add(historyData);
                        }

                        var text = msg["text"]?.ToString();
                        if (!string.IsNullOrEmpty(text)) {
                            Debug.Log("[AI]:" + text);
                            _chatUI.AddText(text);
                            _chatModel.m_HistoryDataList.Last().Content += text;
                            this.SendEvent<AvatarTextEvent>();
                        }
                    } else if (state == "stop") {
                        if (_chatModel.m_HistoryDataList.Count != 0) {
                            var role = _chatModel.m_HistoryDataList.Last().Role;
                            if (role == "assistant") {
                                var content = _chatModel.m_HistoryDataList.Last().Content;
                                var isNotQuestion = !content.Contains("?") && !content.Contains("？");
                                // 如果任务已经进行三分钟之后，ASD-LLM 回复中没有问号，则认为其判定对话结束
                                // 此处不使用 _taskModel.completionDegree  ASD模型
                                var isTaskDone = (Time.time - _chatModel.OneChatStartTimestamp) > (60 * 3);
                                if (_taskModel.taskInfo.llmName == ASDLLM && isNotQuestion && isTaskDone) {
                                    _chatModel.IsTimeout = true;
                                    return;
                                }
                            }
                        }

                        // Debug.Log("TTS播放结束，进入冷却期");
                        WaitForTtsActuallyFinished().Forget();
                    }

                    break;
                }
                case "goodbye":
                    sessionId = null;
                    listenState = "stop";
                    break;
                case "iot":
                    HandleIotMessage(msg);
                    break;
                case "error":
                {
                    var module = msg["error_module"]?.ToString();  // 错误设计的服务模块名称
                    var errorType = msg["error_type"]?.ToString();  // 具体错误类型
                    var errorMessage = msg["error_message"]?.ToString();  // 错误信息
                    Debug.LogError($"错误: {module}, {errorType}, {errorMessage}");
                    // 三种情况：
                    // 1. 由于音频原因，没有识别到文本：
                    // 2. 偶然的错误：偶然的超时等情况
                    // 3. 服务端错误：服务端错误，需要联系管理员
                    // 这里主要需要把第一种情况准确捕获并处理

                    if (errorType == "no_valid_voice") {
                        // 服务端自动按“儿童无响应”处理，pass
                    } else if (module is "stt" or "llm" or "tts" or "asr") {
                        // 偶然的错误：偶然的超时等情况,让用户重新说话即可
                        _audioManager.PlayLocalAudio(Util.Pardon2AudioPath, Util.Pardon2AudioText, _chatUI).ToCoroutine();
                    } else {
                        // 服务端错误：服务端错误，需要联系管理员
                        // 弹窗，让用户联系管理员，然后退出APP
                        ServerError();
                    }

                    break;
                }
            }
        } catch (JsonException ex) {
            Debug.LogError($"JSON解析错误: {ex.Message}\n消息内容: {message}");
        } catch (Exception ex) {
            Debug.LogError($"消息处理错误: {ex.Message}\n{ex.StackTrace}\n消息内容: {message}");
        }
    }

    private async void OnClose() {
        // 服务端主动断开连接事件
        // 根据时间判断，发送哪种消息类型
        // +10秒，是防止设定任务时间过短时（30s），被误判为任务未完成。
        var usedTime = _chatModel.lastUserSpeakEndTime - _chatModel.OneChatStartTimestamp + 10;
        var isNormalClose = usedTime / _taskModel.taskInfo.time >= _taskModel.completionDegree;
        var mChatOverStates = isNormalClose ? ChatOverStates.Normal : ChatOverStates.Error;
        while (isPlaying)
        {
            await UniTask.Delay(100);
        }
        this.SendEvent(new ChatOverEvent(mChatOverStates));

        if(_wsManager == null) return;
        _wsManager.OnOpen -= OnOpen;
        _wsManager.OnMessage -= OnMessage;
        _wsManager.OnClose -= OnClose;
    }

    private async UniTaskVoid WaitForTtsActuallyFinished()
    {
        // 等待播放真正完成
        while (!_audioManager.JudgePlayStateEnd())
        {
            await UniTask.Delay(100);
        }
        Debug.Log("AI Speak End");
        lastTtsEndTime.Restart();
        this.SendEvent(new AvatarSpeakEndEvent());

        // 真正播放完毕，开始监听
        ClientStartListening();
    }

    // 添加延迟启动监听的方法
    private void ClientStartListening() {
        Debug.Log("客户端开始监听");
        isPlaying = false;
        isTtsStarted = false;
        listenState = "start";
        startWaitingTimestamp = Time.time;

        // Debug.Log("我正在竖起耳朵听呢...");
        _chatUI.SetTipsText("我正在竖起耳朵听呢");
        this.SendEvent(new AvatarFeedbackEvent(0));
    }

    private void ServerError() {
        _wsManager.Disconnect();
        _audioManager.PlayLocalAudio(Util.ServerErrorPath, Util.ServerErrorText).ToCoroutine();
        var info = new InfoConfirmInfo("", Util.ServerErrorText, Util.AppQuit, type: ConfirmAlertType.Single);
        this.SendEvent(new ShowPageEvent(UIPageType.InfoConfirmAlert, UILevelType.Alart, info));
    }

    /// <summary>
    ///     设备端开始或停止录音监听
    /// </summary>
    /// <param name="state">"start", "stop", "detect"（唤醒检测已触发）</param>
    /// <param name="mode">"auto", "manual" 或 "realtime"，表示识别模式。</param>
    private void SendListenMsg(string state, string mode) {
        if (_wsManager == null) return;
        // WebSocketStates.Open 不顶用，会是Connecting
        if (_wsManager.State is WebSocketStates.Closing or WebSocketStates.Closed) return;
        var listenMsg = new {
            session_id = sessionId,
            type = "listen",
            state,
            mode
        };
        var json = JsonConvert.SerializeObject(listenMsg);
        _wsManager.SendMessage(json);
    }

    /// <summary>
    ///     开始监听
    /// 本应是客户端虚拟人说完话，即开始监听用户说话，然后触发此函数。（客户端开始监听）
    /// 此处修改为：客户端监听到用户开始说话，然后再触发此函数，然后服务端开始监听。（服务端开始监听）
    /// 即修改了服务端监听的时机。
    ///  需要注意，此函数与StopListening()函数，不完全对称
    /// </summary>
    /// <param name="mode"></param>
    private void StartListening(string mode) {
        lastPosition = Microphone.GetPosition(null);
        SendListenMsg(listenState, mode);
        startSpeakTimestamp = Time.time;
    }

    /// <summary>
    ///     手动开始监听-按钮
    /// </summary>
    public void StartButtonRecording() {
        // 重置播放缓冲
        _audioManager.ResetPlayback();

        if (ttsState == "start" || ttsState == "sentence_start") {
            _wsManager.SendMessage(JsonConvert.SerializeObject(new { type = "abort" }));
            isPlaying = false;
        }
        isSpeaking = true;
        isListening = true;
        listenState = "start";
        StartListening("manual");
    }

    /// <summary>
    /// 手动停止监听-按钮
    /// </summary>
    public void StopButtonRecording() {
        StopListening("manual");
    }

    /// <summary>
    ///     停止监听
    /// </summary>
    /// <param name="mode"></param>
    private void StopListening(string mode) {
        listenState = "stop";
        SendListenMsg(listenState, mode);
        this.SendEvent<UserSpeakEndEvent>();
    }

    /// <summary>
    ///     发送音频数据
    /// </summary>
    private async UniTask SendAudioCoroutine() {
        lastPosition = 0;
        isChatEnd = false;
        var accumulatedSamples = new List<float>(); // 用于累积样本
        try {
            while (true) {
                if (isPlaying) {
                    accumulatedSamples.Clear();
                    isSpeaking = false;
                    isListening = false;
                    currentSilenceTimes.Restart();
                    lastPosition = Microphone.GetPosition(null);
                    await UniTask.Yield();
                    continue;
                }

                if (_wsManager is { State: WebSocketStates.Closed or WebSocketStates.Closing }) {
                    Debug.Log("达到对话时长，SendAudioCoroutine 协程退出");
                    break;
                }

                if (!isSpeaking) {
                    // 没有播放，也不在用户说话期间，检测是否达到对话时间，达到则发送结束消息
                    // 让服务端发送总结对话，客户端不再发送额外消息
                    if (_chatModel.IsTimeout && !isChatEnd) {
                        isChatEnd = true;
                        isListening = false;
                        SendChatEndMessage();
                        isPlaying = false;
                        lastTtsEndTime.Restart();
                        break;
                    }
                }

                if (_chatModel.Pause.Value) {
                    _wsManager.SendMessage(JsonConvert.SerializeObject(new { type = "abort" }));
                    isSpeaking = false;
                    isListening = false;
                    await UniTask.WaitUntil(() => !_chatModel.Pause.Value);
                    continue;
                }

                if (_chatModel.IsQuit) {
                    Debug.Log("用户主动退出，SendAudioCoroutine 协程退出");
                    break;
                }

                if (listenState == "start") {
                    var position = Microphone.GetPosition(null);
                    if (position < lastPosition) {
                        // 录音剪辑已循环：先读取从 lastPosition 到剪辑末尾的数据
                        var samplesToEnd = _audioManager.MainClip.samples - lastPosition;
                        var tailSamples = new float[samplesToEnd];
                        _audioManager.MainClip.GetData(tailSamples, lastPosition);
                        accumulatedSamples.AddRange(tailSamples);

                        // 再读取从剪辑开始到当前位置的数据
                        if (position > 0) {
                            var headSamples = new float[position];
                            _audioManager.MainClip.GetData(headSamples, 0);
                            accumulatedSamples.AddRange(headSamples);
                        }

                        lastPosition = position;
                    } else if (position > lastPosition) {
                        // 正常情况：读取从 lastPosition 到 position 的数据
                        var samplesToRead = position - lastPosition;
                        var samples = new float[samplesToRead];
                        _audioManager.MainClip.GetData(samples, lastPosition);
                        accumulatedSamples.AddRange(samples);
                        lastPosition = position;

                        // 当累积样本达到或超过帧大小时，进行编码并发送
                        while (accumulatedSamples.Count >= _audioManager.recordFrameSize * _audioManager.channels) {
                            var frameSamples = accumulatedSamples
                                .GetRange(0, _audioManager.recordFrameSize * _audioManager.channels).ToArray();
                            accumulatedSamples.RemoveRange(0, _audioManager.recordFrameSize * _audioManager.channels);

                            // 添加VAD检测
                            if (!IsManualMode()) {
                                var isAndroid = Application.platform == RuntimePlatform.Android;
                                // var hasVoice = isAndroid ? _vadSystem.HasSpeech(WavUtility.ConvertToPCM(frameSamples)) : DetectVoiceActivity(frameSamples);
                                var hasVoice = DetectVoiceActivity(frameSamples);

                                var offset = Microphone.GetPosition(_audioManager.MicrophoneDevice) - 128 + 1;
                                if (offset < 0) return;
                                var volumeData = new float[128];
                                _audioManager.MainClip.GetData(volumeData, offset);

                                if (hasVoice) {
                                    // 检测到语音
                                    currentSilenceTimes.Restart();
                                    if (!isListening) {
                                        // 检查是否刚从TTS播放结束不久
                                        if (lastTtsEndTime.Elapsed.TotalSeconds * 1000 < ttsCooldownTime_ms * 3) {
                                            Debug.Log(
                                                $"VAD: TTS结束后{lastTtsEndTime.Elapsed.TotalSeconds:F2}秒内检测到声音，可能是回音，忽略");
                                            continue;
                                        }

                                        isListening = true;
                                        startSpeakTimestamp = Time.time;
                                        StartListening("auto");

                                        Debug.Log("StartListening");
                                    }
                                }

                                OnGetSpectrumData.Invoke(isListening, isSpeaking, volumeData);
                            }

                            if (!isListening) continue;
                            var opusData = _audioManager.EncodeAudio(frameSamples);
                            if (opusData == null) continue;
                            if (_wsManager == null) return;
                            // WebSocketStates.Open 不顶用，会是Connecting
                            if (_wsManager.State is WebSocketStates.Closing or WebSocketStates.Closed) return;
                            // 检查编码是否成功
                            _wsManager.SendBinary(opusData);
                        }
                    }
                }

                await UniTask.Yield();
            }
        } catch (Exception e) {
            Debug.LogError(e.Message);
        }
    }

    // 添加VAD检测方法 低功耗 + 高召回 宁可误触发，不可漏触发”，低功耗优先，服务端兜底
    private bool DetectVoiceActivity(float[] samples) {
        // 简单的能量检测方法
        float energy = 0;
        for (var i = 0; i < samples.Length; i++) energy += samples[i] * samples[i];
        energy /= samples.Length;

        // 在TTS播放结束后的一段时间内，提高VAD阈值，减少误触发
        var currentThreshold = vadThreshold;
        if (lastTtsEndTime.Elapsed.TotalSeconds * 1000 < ttsCooldownTime_ms * 2) {
            // 在冷却期后的一段时间内，使用更高的阈值
            currentThreshold = vadThreshold * 1.2f;

            // 如果能量值接近阈值但未超过，记录日志但不触发
            if (energy > vadThreshold && energy <= currentThreshold)
                Debug.Log($"VAD: 冷却期内检测到低能量声音，能量值: {energy:F9}，当前阈值: {currentThreshold:F5}");
        }

        // 判断能量是否超过阈值
        var hasVoice = energy > currentThreshold;

        // 可以添加调试信息，帮助调整阈值
        // if (hasVoice) Debug.Log($"VAD: 检测到声音，能量值: {energy:F9}，当前阈值: {currentThreshold:F5}");
        // Debug.Log($"VAD: 能量值: {energy:F9}，当前阈值: {currentThreshold:F5}");

        return hasVoice;
    }

    /// <summary>
    /// 发送聊天配置消息
    /// </summary>
    private void SendChatConfigMessage() {
        var gender = this.GetModel<ICharacterModel>().CharacterDic[_userModel.Cid.Value].gender;
        var theme = $"对话主题：{_taskModel.taskInfo.name}。 参考方向：{_taskModel.taskInfo.prompt} ";
        var rate = this.GetModel<IConfigModel>().Voice.Rate.Value; // 语速 0.4-1.0
        var userName = _userModel.UserName.Value;

        var atisMsg = new {
            type = "atis",
            state = "chat",
            voice_config = new {
                rate, // 语速 0.4-1.0
                gender // boy girl
            },
            theme,
            userName,
            sessionId
        };
        var json = JsonConvert.SerializeObject(atisMsg);
        _wsManager.SendMessage(json);
    }

    /// <summary>
    /// 发送聊天结束消息,服务端总结对话，断开连接
    /// </summary>
    private void SendChatEndMessage() {
        var chatEndMsg = new {
            type = "atis",
            state = "chat_end"
        };
        var json = JsonConvert.SerializeObject(chatEndMsg);
        _wsManager.SendMessage(json);
    }

    /// <summary>
    ///     发送 Iot设备描述同步到服务端
    /// </summary>
    /// <returns></returns>
    private void SendIotDescriptors() {
        try {
            var thingManager = ThingManager.GetInstance();
            var descriptorsJson = thingManager.GetDescriptorsJson();

            // 解析为对象
            var descriptorsObj = JObject.Parse(descriptorsJson);

            // 添加 session_id
            descriptorsObj["session_id"] = sessionId;

            // 直接发送对象，而不是字符串
            _wsManager.SendMessage(descriptorsObj.ToString(Formatting.None));

            Debug.Log($"已发送IoT设备描述\n{descriptorsObj}");
        } catch (Exception ex) {
            Debug.LogError($"发送IoT设备描述失败: {ex.Message}");
        }
    }

    /// <summary>
    ///     处理物联Iot网消息
    /// </summary>
    /// <param name="data">带有iot的json数据</param>
    private async void HandleIotMessage(JObject data) {
        try {
            // 检查消息类型
            var type = data["type"]?.ToString();
            if (type != "iot") {
                Debug.LogError($"非物联网消息类型: {type}");
                return;
            }

            // 获取命令数组
            var commands = data["commands"] as JArray;
            if (commands == null || commands.Count == 0) {
                Debug.LogError("物联网命令为空或格式不正确");
                return;
            }

            foreach (JObject command in commands)
                try {
                    // 记录接收到的命令
                    var mes = command.ToString(Formatting.None);

                    // 如果MQTT服务已初始化，则发布消息
                    mqttService?.PublishAsync(mes).ConfigureAwait(false);

                    Debug.Log($"收到物联网命令: {mes}");

                    // 执行命令
                    var result = await thingManager.Invoke(command);
                    Debug.Log($"执行物联网命令结果: {result}");

                    // 命令执行后更新设备状态
                    UpdateIotStates();
                } catch (Exception ex) {
                    Debug.LogError($"执行物联网命令失败: {ex.Message}");
                }
        } catch (Exception ex) {
            Debug.LogError($"处理物联网消息失败: {ex.Message}");
        }
    }

    private void UpdateIotStates() {
        try {
            if (thingManager == null) {
                Debug.LogError("ThingManager未初始化，无法更新状态");
                return;
            }

            // 获取当前设备状态
            var statesJson = thingManager.GetStatesJson();
            if (string.IsNullOrEmpty(statesJson)) {
                Debug.LogError("获取设备状态失败，返回空JSON");
                return;
            }

            // 发送状态更新
            if (_wsManager != null) {
                _wsManager.SendMessage(statesJson);
                Debug.Log("物联网设备状态已更新");
            } else {
                Debug.LogError("WebSocket管理器未初始化，无法发送状态更新");
            }
        } catch (Exception ex) {
            Debug.LogError($"更新物联网状态失败: {ex.Message}\n{ex.StackTrace}");
        }
    }

    private bool IsManualMode() {
        return _chatModel.chatType.Value == ChatType.RecordInput;
    }

    public void SwitchChatType(ChatType toChatType) {
        // 默认只有录音模式和实时对话模式两种模式相互转换
        // 保证切换模式后，如果虚拟人没有说话，按钮等都可触发
        if (!isPlaying) this.SendEvent(new AvatarSpeakEndEvent());
        switch (toChatType)
        {
            case ChatType.RecordInput:
                // StartListening("manual");
                break;
            case ChatType.VoiceInput:
                if (!isPlaying) listenState = "start";
                break;
        }
    }

    /// <summary>
    /// 聊天结束事件
    /// 需要断开连接，结束所有流程
    /// </summary>
    /// <param name="e"></param>
    private void ChatOver(ChatOverEvent e) {
        _wsManager.SendMessage(JsonConvert.SerializeObject(new { type = "abort" }));
        _audioManager.ResetPlayback();

        if(_wsManager == null) return;
        _wsManager.OnOpen -= OnOpen;
        _wsManager.OnMessage -= OnMessage;
        _wsManager.OnClose -= OnClose;
        _wsManager.Disconnect();
    }

    private void OnUserSpeakEnd(UserSpeakEndEvent e) {
        // Debug.Log("正在思考中...");
        _chatUI.SetTipsText("正在思考中...");
        this.SendEvent(new AvatarFeedbackEvent(1));
    }

    #region 字段属性

    private string sessionId { get; set; }
    private string ttsState = "idle";
    private string listenState = "stop";
    private bool isPlaying;
    private bool isTtsStarted; // TTS 是否已开始
    private MqttService mqttService;

    private ThingManager thingManager = ThingManager.GetInstance();

    // VAD相关参数
    private float vadThreshold = 0.02f; // VAD阈值，可根据环境噪音调整
    private float vadSilenceTimes = 2; // 静音帧数阈值(约0.5秒)
    private readonly Stopwatch currentSilenceTimes = new(); // 当前连续静音时间
    private bool isSpeaking; // 是否检测到说话（服务端），为真，则表示服务端检测到收到的音频开始有人声，然后通知到客户端。
    private bool isListening; // 是否开始监听, 为真，则可以发送音频数据到服务端，即表示开始监听的含义
    private Stopwatch lastTtsEndTime = new(); // 上次TTS结束的时间，音频播放结束时间
    private readonly int ttsCooldownTime_ms = 150; // TTS结束后的冷却时间(毫秒)
    private int lastPosition; // 上次读取的位置

    // 数据记录
    private float startWaitingTimestamp;  // 开始等待时间戳：虚拟人说完话
    private float startSpeakTimestamp;  // 开始说话时间戳：用户说话
    private float stopSpeakTimestamp;  // 结束说话时间戳：用户停止说话

    private bool isChatEnd; // 聊天是否结束

    [Serializable]
    private class OtaApplication {
        public string name;
        public string version;
    }

    [Serializable]
    private class OtaPostData {
        public OtaApplication application;
    }

    [Serializable]
    private class OtaActivation {
        public string code;
    }

    [Serializable]
    private class OtaFirmware {
        public string version;
    }

    [Serializable]
    private class OtaResponse {
        public OtaActivation activation;
        public OtaFirmware firmware;
    }

    #endregion
}
