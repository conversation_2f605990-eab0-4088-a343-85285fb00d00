using QFramework;
using UnityEngine;

public class InitNetworkState : AbstractState<LaunchStates, Launch>, IController {
    public InitNetworkState(FSM<LaunchStates> fsm, Launch target) : base(fsm, target) {
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    protected override void OnEnter() {
        Debug.Log("InitNetworkState Enter");
        this.SendCommand(new InitNetworkCommand());
        ChangeState();
    }

    private void ChangeState() {
        mFSM.ChangeState(LaunchStates.PrepareUI);
    }

    protected override void OnExit() {
    }
}
