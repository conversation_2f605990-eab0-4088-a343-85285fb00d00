﻿using QFramework;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class RecordButton : Mono<PERSON><PERSON><PERSON><PERSON>, IController {
    /// <summary>
    ///     录音按钮的文本
    /// </summary>
    [SerializeField] private Text m_VoiceBottonText;

    private Button _button;

    private IVoiceAssistantClientSystem _voiceAssistantClientSystem;

    private bool isSpeak; // 用户正在录音说话

    // Start is called before the first frame update
    private void Awake() {
        _voiceAssistantClientSystem = this.GetSystem<IVoiceAssistantClientSystem>();
        _button = gameObject.GetComponent<Button>();
        RegistButtonEvent();
        this.RegisterEvent<AvatarSpeakEndEvent>(e => {
            var interactable = !this.GetModel<IChatModel>().IsTimeout;
            _button.interactable = interactable;
        }).UnRegisterWhenGameObjectDestroyed(gameObject);
        this.RegisterEvent<UserSpeakEndEvent>(e => {
                _button.interactable = false;
                m_VoiceBottonText.text = "按住按钮，开始录音";
                isSpeak = false;
            })
            .UnRegisterWhenGameObjectDestroyed(gameObject);
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    /// <summary>
    ///     注册按钮事件
    /// </summary>
    private void RegistButtonEvent() {
        var _trigger = gameObject.AddComponent<EventTrigger>();

        //添加按钮按下的事件
        var _pointDown_entry = new EventTrigger.Entry();
        _pointDown_entry.eventID = EventTriggerType.PointerDown;
        _pointDown_entry.callback = new EventTrigger.TriggerEvent();

        //添加按钮松开事件
        var _pointUp_entry = new EventTrigger.Entry();
        _pointUp_entry.eventID = EventTriggerType.PointerUp;
        _pointUp_entry.callback = new EventTrigger.TriggerEvent();

        //添加委托事件
        _pointDown_entry.callback.AddListener(delegate { StartRecord(); });
        _pointUp_entry.callback.AddListener(delegate { StopRecord(); });

        _trigger.triggers.Add(_pointDown_entry);
        _trigger.triggers.Add(_pointUp_entry);
    }

    /// <summary>
    ///     开始录制
    /// </summary>
    private void StartRecord() {
        if (gameObject.GetComponent<Button>().interactable == false) {
            isSpeak = false;
            return;
        }

        if (!isSpeak) {
            m_VoiceBottonText.text = "正在录音中...";
            _voiceAssistantClientSystem.StartButtonRecording();
            isSpeak = true;
        }
    }

    /// <summary>
    ///     结束录制
    /// </summary>
    private void StopRecord() {
        if (gameObject.GetComponent<Button>().interactable == false) {
            m_VoiceBottonText.text = "按住按钮，开始录音";
            isSpeak = false;
            return;
        }

        if (!isSpeak) {
            var alertInfo = new WarningAlertInfo("按太快了！\n请重新录音！");
            this.SendCommand(new ShowPageCommand(UIPageType.WarningAlert, UILevelType.Alart, alertInfo));
            m_VoiceBottonText.text = "按住按钮，开始录音";
            isSpeak = false;
            return;
        }

        m_VoiceBottonText.text = "按住按钮，开始录音";
        _voiceAssistantClientSystem.StopButtonRecording();
        isSpeak = false;
    }
}
