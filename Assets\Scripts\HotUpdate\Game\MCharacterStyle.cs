using Cysharp.Threading.Tasks;
using QFramework;
using UnityEngine;
using UnityEngine.ResourceManagement.AsyncOperations;
using UnityEngine.SceneManagement;
using Utils;

public class MCharacterStyle : MonoBehaviour, IController {
    public TextMesh nameText;
    public Transform carPos;
    public GameObject car;

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }
    // public RuntimeAnimatorController runtimeAnimatorController;

    public async UniTask ChangeCharacter(string cid) {
        var obj = await this.GetSystem<IAddressableSystem>().LoadAssetAsync<GameObject>(Util.GetCharacterUrl(cid));
        if (obj.Status == AsyncOperationStatus.Succeeded) {
            if (car != null) {
                car.transform.SetParent(null);
                Destroy(car);
            }

            if (obj.Result == null || carPos == null) return;
            if (nameText != null) nameText.text = cid;
            Debug.Log("加载3D模型：" + cid);
            car = Instantiate(obj.Result);
            car.transform.SetParent(carPos.transform, false);
            // car.transform.localPosition = carPos.localPosition;

            Debug.Log(SceneManager.GetActiveScene().name);
            Debug.Log(ScenePath.Task);
            if (SceneManager.GetActiveScene().name == SceneID.Task.ToString()) {
                var gender = this.GetModel<ICharacterModel>().CharacterDic[this.GetModel<IUserModel>().Cid.Value]
                    .gender;
                var obj1 = await this.GetSystem<IAddressableSystem>()
                    .LoadAssetAsync<RuntimeAnimatorController>(Util.GetCharacterAnimatorUrl(gender));
                if (obj1.Status == AsyncOperationStatus.Succeeded) {
                    car.transform.RecursiveFindChild("KekosCharacter").gameObject.GetComponent<Animator>()
                        .runtimeAnimatorController = Instantiate(obj1.Result);
                    this.SendCommand(new ChangeCharacterSucceedCommand(car));
                }

                return;
            }

            this.SendCommand(new ChangeCharacterSucceedCommand(car));
        }
    }
}
