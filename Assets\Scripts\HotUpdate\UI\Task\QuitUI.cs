﻿using QFramework;
using UnityEngine;
using UnityEngine.UI;
using Utils;

public class QuitUI : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IController {
    public Button QuitBtn;
    public Button CancelBtn;

    public void Start() {
        QuitBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            gameObject.SetActiveFast(false);

            var _taskModel = this.GetModel<ITaskModel>();
            var _chatModel = this.GetModel<IChatModel>();

            var lastUserSpeakEndTime = _chatModel.lastUserSpeakEndTime;
            var usedTime = lastUserSpeakEndTime - _chatModel.OneChatStartTimestamp;
            var isNormalClose = usedTime / _taskModel.taskInfo.time >= _taskModel.completionDegree;
            var mChatOverStates = isNormalClose ? ChatOverStates.Normal : ChatOverStates.Error;
            this.SendCommand(new ChatOverCommand(mChatOverStates));
        });

        CancelBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            gameObject.SetActiveFast(false);

            this.SendCommand(new ShowPageCommand(UIPageType.PauseUI)); // TODO 不一定是显示暂停页面，也可能是结算页面
        });
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }
}
