﻿using System;
using DTC;
using QFramework;
using UnityEngine;
using Utils;

public class DeviceInfo {
    public string Name { get; set; }
    public string BatteryLevel { get; set; }
    public string Address { get; set; }
    public string HardwareVersion { get; set; }
    public string FirmwareVersion { get; set; }
}

public interface IEegModel : IModel {
    public DeviceInfo deviceInfo { get; set; }
    public BindableProperty<bool> isActive { get; set; }
    public BindableProperty<EegStatusEnum> eegStatus { get; set; }
    public BluetoothManager bluetoothManager { get; set; }
    public EnterTechBleManager enterTechBleManager { get; set; }
    public BindableProperty<bool> isCorrected { get; set; }
    public BindableProperty<bool> isCorrectedRoot { get; set; } // Root界面设置是否已脑电校正的标记，用于测试
    public string CalibrationEEGDataFilePath { get; set; }
    public string EEGPath { get; set; }
    public BindableProperty<string> LastResetTime { get; set; } // 最后校正脑电日期
    public bool IsRecording { get; set; } // 允许记录脑电数据
    public string EegStatus();
    public string FixEeg();
    public void SetEegStatus(EegStatusEnum e);
}

public class EegModel : AbstractModel, IEegModel {
    public DeviceInfo deviceInfo { get; set; } = new();
    public BindableProperty<bool> isActive { get; set; } = new();
    public BindableProperty<EegStatusEnum> eegStatus { get; set; } = new();
    public BluetoothManager bluetoothManager { get; set; } = new();
    public EnterTechBleManager enterTechBleManager { get; set; } = new();
    public BindableProperty<bool> isCorrected { get; set; } = new();
    public BindableProperty<bool> isCorrectedRoot { get; set; } = new();
    public string CalibrationEEGDataFilePath { get; set; } = string.Empty;
    public string EEGPath { get; set; } = string.Empty;
    public BindableProperty<string> LastResetTime { get; set; } = new();
    public BindableProperty<string> LastResetTicks { get; set; } = new();
    public bool IsRecording { get; set; } = false;

    public string EegStatus() {
        switch (eegStatus.Value) {
            case EegStatusEnum.Inactive:
                return "EEG 未开启";
            case EegStatusEnum.IsUnBindDevice:
                return "EEG 未绑定设备";
            case EegStatusEnum.Scanning:
                return "EEG 扫描中";
            case EegStatusEnum.Disconnected:
                return "EEG 未连接";
            case EegStatusEnum.Connecting:
                return "EEG 正在连接";
            case EegStatusEnum.Connected:
                return "EEG 已连接";
            case EegStatusEnum.NoForehead:
                return "EEG 未贴额头";
            case EegStatusEnum.Calibrating:
                return "脑电校正中";
            case EegStatusEnum.Error:
                return "未找到脑电设备";
            default:
                return "EEG 404";
        }
    }

    public string FixEeg() {
        switch (eegStatus.Value) {
            case EegStatusEnum.Inactive:
                return "EEG设备 未开启";
            case EegStatusEnum.IsUnBindDevice:
                return "设备已解绑，请扫描连接设备";
            case EegStatusEnum.Scanning:
                return "EEG 扫描中";
            case EegStatusEnum.Disconnected:
                return "设备连接已断开，请扫描连接设备";
            case EegStatusEnum.Connecting:
                return "设备正在连接，请等待连接完成";
            case EegStatusEnum.Connected:
                return "设备已连接，且佩戴完好";
            case EegStatusEnum.NoForehead:
                return "请佩戴好设备";
            case EegStatusEnum.Calibrating:
                return "脑电校正中";
            case EegStatusEnum.Error:
                return "未找到脑电设备，请再次扫描连接";
            default:
                return "EEG 404";
        }
    }

    public void SetEegStatus(EegStatusEnum e) {
        if (eegStatus.Value != e) Debug.Log("eegStatus: " + eegStatus.Value);
        if (!isActive.Value) {
            eegStatus.Value = EegStatusEnum.Inactive;
            return;
        }

        eegStatus.Value = e;
    }

    private void InitEegStatus(bool v) {
        if (!v)
            SetEegStatus(EegStatusEnum.Inactive);
        else if (bluetoothManager.IsBindedDevice)
            SetEegStatus(EegStatusEnum.Disconnected);
        else
            SetEegStatus(EegStatusEnum.IsUnBindDevice);
    }

    protected override void OnInit() {
        bluetoothManager = BluetoothManager.Instance;
        enterTechBleManager = EnterTechBleManager.Instance;
        isActive.Value = PlayerPrefs.GetInt(PrefKeys.isActiveEEG) == 0; // 初始是0，0是激活，这样默认就是激活
        // isActive.Value = Application.platform == RuntimePlatform.Android;
        isActive.Register(v => {
            PlayerPrefs.SetInt(PrefKeys.isActiveEEG, v ? 0 : 1);
            InitEegStatus(v);
        });
        InitEegStatus(isActive.Value);

        #region TODO: 后期程序稳定后删除

        isCorrected.Register(v => {
            PlayerPrefs.SetInt(PrefKeys.isCorrectedEEG, v ? 1 : 0);
            // LastResetTime.Value = Util.NowDate(); // 更新最后重置日期
            PlayerPrefs.SetString(PrefKeys.LastResetTicks, DateTime.Now.ToBinary().ToString()); // 更新
        });

        // // 读取上次重置时间（如果没有，则默认最小值）
        // LastResetTime.Value = PlayerPrefs.GetString(PrefKeys.LastResetTime);
        // LastResetTime.Register(v => { PlayerPrefs.SetString(PrefKeys.LastResetTime, v); });

        var lastResetTicks = long.Parse(PlayerPrefs.GetString(PrefKeys.LastResetTicks, "0"));
        var lastResetTime = lastResetTicks != 0 ? DateTime.FromBinary(lastResetTicks) : DateTime.MinValue;

        // 计算天数差
        var daysSinceLastReset = (DateTime.Now - lastResetTime).TotalDays;

        // var currentDate = Util.NowDate();
        // if (LastResetTime.Value != currentDate)
        if (daysSinceLastReset >= 7) {
            isCorrected.Value = false; // 设置为未矫正
            PlayerPrefs.SetString(PrefKeys.LastResetTicks, DateTime.Now.ToBinary().ToString()); // 更新重置时间
        }
        else
            // 如果不需要重置，从PlayerPrefs加载变量
            isCorrected.Value = PlayerPrefs.GetInt(PrefKeys.isCorrectedEEG) == 1; // 如果没有存储，默认为0，即未矫正

        isCorrectedRoot.Value = false; // 默认为false，放置没有被关闭
        isCorrectedRoot.Register(v => { PlayerPrefs.SetInt(PrefKeys.isCorrectedEEGRoot, v ? 1 : 0); });

        #endregion
    }
}

public enum EegStatusEnum {
    Inactive = 1, // 未激活 未开启
    IsUnBindDevice, // 未绑定设备
    Scanning, // 扫描中
    Disconnected, // 未连接
    Connecting, // 正在连接
    Connected, // 已连接
    NoForehead, // 未贴额头
    Calibrating, // 校正中
    Error // 错误
}
