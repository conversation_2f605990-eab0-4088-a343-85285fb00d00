﻿using System;
using QFramework;

public class ConnectBleEegCommand : AbstractCommand {
    protected override void OnExecute() {
        this.SendEvent<ConnectBleEegEvent>();
    }
}

public class StartCollectEegCommand : AbstractCommand {
    protected override void OnExecute() {
        this.SendEvent<StartCollectEegEvent>();
    }
}


public class CheckEegStatusCommand : AbstractCommand {
    private readonly Action mAction;
    private IEegModel eegModel;

    public CheckEegStatusCommand(Action action) {
        mAction = action;
    }

    protected override void OnExecute() {
        eegModel = this.GetModel<IEegModel>();

        // 未开启，则进入
        if (eegModel.eegStatus.Value == EegStatusEnum.Inactive) {
            mAction.Invoke();
        } else {
            if (eegModel.isCorrected.Value || eegModel.isCorrectedRoot.Value) {
                if (eegModel.eegStatus.Value == EegStatusEnum.Connected) {
                    mAction.Invoke();
                } else {
                    if (eegModel.eegStatus.Value == EegStatusEnum.Disconnected)
                        this.SendCommand<ConnectBleEegCommand>();

                    // 弹出当前EEG状态提示框
                    var info = new InfoConfirmInfo(content: eegModel.FixEeg(),
                        success: () => {
                            // this.SendCommand(new ShowPageCommand(UIPageType.EEGBluetoothUI));
                        },
                        type: ConfirmAlertType.Single);
                    this.SendCommand(new ShowPageCommand(UIPageType.InfoConfirmAlert, UILevelType.Alart, info));
                }
            } else {
                var info = new InfoConfirmInfo(content: "每日训练前，\n需进行脑机校准！",
                    success: () => {
                        // 确认后,弹出EEG UI
                        this.SendCommand(new ShowPageCommand(UIPageType.EEGBluetoothUI));
                    },
                    type: ConfirmAlertType.Single);
                this.SendCommand(new ShowPageCommand(UIPageType.InfoConfirmAlert, UILevelType.Alart, info));
            }
        }
    }
}

public class EegScanCommand : AbstractCommand {
    protected override void OnExecute() {
        this.SendEvent<EegScanEvent>();
    }
}

public class GetEegBatteryCommand : AbstractCommand {
    protected override void OnExecute() {
        this.SendEvent<GetEegBatteryEvent>();
    }
}

public class ShowEegBatteryCommand : AbstractCommand {
    protected override void OnExecute() {
        this.SendEvent<ShowEegBatteryEvent>();
    }
}

public class EEGCalibrationDoneCommand : AbstractCommand {
    protected override void OnExecute() {
        this.SendEvent<EEGCalibrationDoneEvent>();
    }
}

public class EEGConnectedCommand : AbstractCommand {
    protected override void OnExecute() {
        this.SendEvent<EEGConnectedEvent>();
    }
}
