﻿using System.Collections;
using QFramework;
using UnityEngine;
using UnityEngine.ResourceManagement.AsyncOperations;
using UnityEngine.UI;
using Utils;

public class HomepageUI : MonoBehaviour, IController {
    public Button avatarBtn;
    public Image avatarImage;
    public Image vipImage;
    public Button joinGameBtn;
    public Text starText;
    public Text nickNameText;
    public Button optionOpenBtn;
    public Button optionCloseBtn;
    public GameObject optionBtnsGO;
    public Button rootBtn;
    public Button infoBtn;
    public Button questionBtn;
    public Button exitBtn;
    public Button calendarBtn; // 每日记录展示页面：待定
    public Button virtualManBtn; // 虚拟形象
    public Button achievementBtn; // 成就
    public Button questionnaireBtn; // 问卷
    public Button settingBtn; // 设置
    public Button EegBtn;

    private async void Awake() {
        var cid = "001";
        await this.GetSystem<IAddressableSystem>()
            .LoadAssetAsync<GameObject>(Util.GetCharacterUrl(cid));

        await this.GetSystem<IAddressableSystem>()
            .LoadAssetAsync<Sprite>(Util.GetCharacterSpriteUrl(cid));
    }

    private void Start() {
        print("HomepageUI Start");
        this.GetSystem<IAudioManagerSystem>().InitStreamingPlayback();

        nickNameText.text = this.GetModel<IUserModel>().Name.Value;
        Util.SetUserChatDataDir(this.GetModel<IUserModel>().Uid.Value + "-" + this.GetModel<IUserModel>().Name.Value);

        avatarBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            this.SendCommand(new ShowPageCommand(UIPageType.ProfileUI));
        });

        joinGameBtn.onClick.AddListener(() => {
            Debug.Log("HomepageUI StartBtn Click");
            if (!this.GetModel<IUserModel>().IsCompleteGuidance.Value)
                this.GetModel<IUserModel>().IsCompleteGuidance.Value = true;
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            //this.SendCommand<GetIsQuestionnaireCommand>();  // 甲方说不要问卷功能了
            var wsUrl = this.GetSystem<IWebSocketSystem>().GetWebSocketUrl();
            this.GetSystem<IWebSocketSystem>().Check(wsUrl,
                () => {
                    this.SendCommand(new CheckEegStatusCommand(() => {
                        this.SendCommand(new UpdateTaskCommand(() => this.SendCommand(new LoadSceneCommand(ScenePath.Task))));
                    }));
                    StartCoroutine(StartBtnCoroutine());
                },
                () =>
                {
                    var info = new InfoConfirmInfo("", "啊哦~AI服务器出错！\n请联系管理员或重启APP！\n点击确定后，将返回首页。",
                        type: ConfirmAlertType.Single);
                    this.SendCommand(new ShowPageCommand(UIPageType.InfoConfirmAlert, UILevelType.Alart, info));
                });
        });

        EegBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            if (this.GetModel<IEegModel>().isActive.Value)
                this.SendCommand(new ShowPageCommand(UIPageType.EEGBluetoothUI));
        });

        //--------- option ---------

        optionBtnsGO.SetActiveFast(false);
        optionCloseBtn.gameObject.SetActiveFast(false);
        optionOpenBtn.gameObject.SetActiveFast(true);
        optionOpenBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            optionBtnsGO.SetActiveFast(true);
            optionOpenBtn.gameObject.SetActiveFast(false);
            optionCloseBtn.gameObject.SetActiveFast(true);
        });
        optionCloseBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Close);
            optionBtnsGO.SetActiveFast(false);
            Util.DelayExecuteWithSecond(0.25f, () => {
                optionCloseBtn.gameObject.SetActiveFast(false);
                optionOpenBtn.gameObject.SetActiveFast(true);
            });
        });

        rootBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            if (this.GetModel<IUserModel>().IsSuperuser.Value)
                this.SendCommand(new ShowPageCommand(UIPageType.RootUI));
            else
                this.SendCommand(new ShowPageCommand(UIPageType.RootPwdUI));
        });

        infoBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            var info = new InfoConfirmInfo(
                "版本",
                "APP版本：" + Application.version + "\n服务环境：" + this.GetSystem<INetworkSystem>().ServerType,
                type: ConfirmAlertType.Single);
            this.SendCommand(new ShowPageCommand(UIPageType.InfoConfirmAlert, UILevelType.Alart, info));
        });

        questionBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            var info = new InfoConfirmInfo("内容", "作者：QJH \n邮箱地址：<EMAIL> \n公司：zjbci"
                , type: ConfirmAlertType.Single);
            this.SendCommand(new ShowPageCommand(UIPageType.InfoConfirmAlert, UILevelType.Alart, info));
        });
        exitBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            // 注释掉，退出后仍可以自动登录
            //this.GetModel<IUserModel>().RememberPassword.Value = 0;
            Util.AppQuit();
        });
        //--------- option ---------

        // --------- Bottom Btns ---------
        calendarBtn.onClick.AddListener(() => { this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low); });
        virtualManBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            this.SendCommand(new ShowPageCommand(UIPageType.ChangeCharacterUI));
            this.SendCommand(new HidePageCommand(UIPageType.HomepageAtisUI));
        });
        achievementBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            // TODO: 打开成就页面 （生涯激励页面）
        });
        questionnaireBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            //this.SendCommand(new ShowPageCommand(UIPageType.QuestionnaireUI));  // 甲方说不要问卷功能了
        });
        settingBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            this.SendCommand(new ShowPageCommand(UIPageType.SettingsUI));
        });
        // --------- Bottom Btns ---------

        OnUpdataUI(new UpdateHomepageUIEvent());
        HandleExitButtonActive(new HandleExitButtonActiveEvent());
        this.RegisterEvent<UpdateHomepageUIEvent>(OnUpdataUI).UnRegisterWhenGameObjectDestroyed(gameObject);
        this.RegisterEvent<HandleExitButtonActiveEvent>(HandleExitButtonActive)
            .UnRegisterWhenGameObjectDestroyed(gameObject);

        this.SendCommand<ConnectBleEegCommand>();
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    private void HandleExitButtonActive(HandleExitButtonActiveEvent e) {
        // 注释掉，不隐藏此按钮
        //exitBtn.gameObject.SetActive(this.GetModel<IUserModel>().IsSuperuser.Value);
    }

    private async void OnUpdataUI(UpdateHomepageUIEvent e) {
        var obj = await this.GetSystem<IAddressableSystem>()
            .LoadAssetAsync<Sprite>(Util.GetAvatarUrl(this.GetModel<IUserModel>().Aid.Value));
        if (obj.Status == AsyncOperationStatus.Succeeded)
            avatarImage.sprite = Instantiate(obj.Result, transform, false);

        starText.text = this.GetModel<IUserModel>().Star.Value.ToString();
        // vipImage.gameObject.SetActiveFast(this.GetModel<IUserModel>().IsVIP.Value);
    }

    private IEnumerator StartBtnCoroutine() {
        yield return new WaitForSeconds(2f);
        joinGameBtn.interactable = true;
    }
}
