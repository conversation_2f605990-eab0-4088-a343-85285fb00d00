﻿public class ShowPageInfo {
    public bool closeOther;
    public object data;
    public UILevelType levelType;
    public UIPageType pageType;

    public ShowPageInfo(UIPageType pageType, bool closeOther = false, UILevelType levelType = UILevelType.UIPage) {
        this.pageType = pageType;
        this.closeOther = closeOther;
        this.levelType = levelType;
        data = null;
    }

    public ShowPageInfo(UIPageType pageType, UILevelType levelType, object data) {
        this.pageType = pageType;
        closeOther = false;
        this.levelType = levelType;
        this.data = data;
    }

    public ShowPageInfo(UIPageType pageType, UILevelType levelType) {
        this.pageType = pageType;
        closeOther = false;
        this.levelType = levelType;
        data = null;
    }

    public ShowPageInfo(UIPageType pageType, bool closeOther, UILevelType levelType, object data) {
        this.pageType = pageType;
        this.closeOther = false;
        this.levelType = levelType;
        this.data = data;
    }
}
