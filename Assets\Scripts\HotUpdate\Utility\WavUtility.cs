using System;
using System.IO;
using UnityEngine;

/// <summary>
///     处理 `AudioClip` 和 WAV 文件之间的转换以及音频文件的保存
/// </summary>
public static class WavUtility {
    /// <summary>
    ///     Converts an AudioClip to a byte array containing a WAV file.
    /// </summary>
    public static byte[] FromAudioClip(AudioClip clip) {
        // Calculate some values to avoid repeated calculations
        var numSamples = clip.samples;
        var numChannels = clip.channels;
        var sampleRate = clip.frequency;
        var bytesPerSample = 2; // 16-bit audio
        var bytesPerSecond = sampleRate * numChannels * bytesPerSample;
        var blockAlign = numChannels * bytesPerSample;

        // Create a new WAV file
        using (var stream = new MemoryStream())
        using (var writer = new BinaryWriter(stream)) {
            // Write the WAV header
            writer.Write(new char[4] { 'R', 'I', 'F', 'F' });
            writer.Write(36 + numSamples * bytesPerSample); // File size (excluding RIFF header)
            writer.Write(new char[4] { 'W', 'A', 'V', 'E' });
            writer.Write(new char[4] { 'f', 'm', 't', ' ' });
            writer.Write(16); // Format chunk size
            writer.Write((ushort)1); // Format type (PCM)
            writer.Write((ushort)numChannels); // Number of channels
            writer.Write(sampleRate); // Sample rate
            writer.Write(bytesPerSecond); // Byte rate
            writer.Write((ushort)blockAlign); // Block align
            writer.Write((ushort)(bytesPerSample * 8)); // Bits per sample
            writer.Write(new char[4] { 'd', 'a', 't', 'a' });
            writer.Write(numSamples * bytesPerSample); // Data chunk size

            // Write the audio data
            var samples = new float[numSamples];
            clip.GetData(samples, 0);
            var intMax = 32767; // max value for a 16-bit signed integer
            foreach (var sample in samples) writer.Write((short)(sample * intMax));

            // Return the byte array
            return stream.ToArray();
        }
    }

    /// <summary>
    ///     byte[] 转换为audioClip
    /// </summary>
    /// <param name="bytes"></param>
    /// <param name="sampleRate"></param>
    /// <returns></returns>
    public static AudioClip ConvertBytesToAudioClip(byte[] bytes, int sampleRate) {
        // 将byte数组转换为float数组
        var floatArray = ConvertBytesToFloat(bytes);

        // 创建AudioClip
        var audioClip = AudioClip.Create("GeneratedAudioClip", floatArray.Length, 1, sampleRate, false);

        // 设置数据
        audioClip.SetData(floatArray, 0);

        return audioClip;
    }

    public static float[] ConvertBytesToFloat(byte[] byteArray) {
        var floatArray = new float[byteArray.Length / 2];
        for (var i = 0; i < floatArray.Length; i++) {
            var value = (short)((byteArray[i * 2 + 1] << 8) | byteArray[i * 2]);
            floatArray[i] = value / 32768.0f;
        }

        return floatArray;
    }

    #region 保存音频文件

    public static void SaveAudioClip(AudioClip clip, string filePath, int length = 0) {
        try {
            // Determine the number of samples to save
            var maxLength = clip.channels * clip.samples;
            var numSamples = length > 0 ? Math.Min(length, maxLength) : maxLength;

            // Get the audio data from the AudioClip
            var samples = new float[numSamples];
            clip.GetData(samples, 0);

            // Create a byte array to store the audio data
            var byteArray = ConvertToPCM(samples);

            // Create the file stream and write the WAV file
            using (var fileStream = new FileStream(filePath, FileMode.Create))
            using (var writer = new BinaryWriter(fileStream)) {
                // Write the WAV header
                WriteWavHeader(writer, clip, numSamples);

                // Write the audio data
                writer.Write(byteArray);
            }

            Debug.Log("AudioClip saved at: " + filePath);
        } catch (Exception ex) {
            Debug.LogError("Failed to save AudioClip: " + ex.Message);
        }
    }

    public static byte[] ConvertToPCM(float[] samples) {
        var pcmData = new byte[samples.Length * 2];
        for (var i = 0; i < samples.Length; i++) {
            var pcmValue = (short)(samples[i] * short.MaxValue);
            pcmData[i * 2] = (byte)(pcmValue & 0xFF);
            pcmData[i * 2 + 1] = (byte)(pcmValue >> 8);
        }

        return pcmData;
    }

    public static void WriteWavHeader(BinaryWriter writer, AudioClip clip, int numSamples) {
        var numChannels = clip.channels;
        var sampleRate = clip.frequency;
        var bytesPerSample = 2; // 16-bit audio
        var bytesPerSecond = sampleRate * numChannels * bytesPerSample;
        var blockAlign = numChannels * bytesPerSample;

        // Write the WAV header
        writer.Write(new char[4] { 'R', 'I', 'F', 'F' });
        writer.Write(36 + numSamples * bytesPerSample); // File size (excluding RIFF header)
        writer.Write(new char[4] { 'W', 'A', 'V', 'E' });
        writer.Write(new char[4] { 'f', 'm', 't', ' ' });
        writer.Write(16); // Format chunk size
        writer.Write((ushort)1); // Format type (PCM)
        writer.Write((ushort)numChannels); // Number of channels
        writer.Write(sampleRate); // Sample rate
        writer.Write(bytesPerSecond); // Byte rate
        writer.Write((ushort)blockAlign); // Block align
        writer.Write((ushort)(bytesPerSample * 8)); // Bits per sample
        writer.Write(new char[4] { 'd', 'a', 't', 'a' });
        writer.Write(numSamples * bytesPerSample); // Data chunk size
    }

    #endregion
}
