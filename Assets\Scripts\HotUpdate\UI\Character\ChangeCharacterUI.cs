﻿using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using QFramework;
using UnityEngine;
using UnityEngine.UI;
using Utils;

public class ChangeCharacterUI : MonoBehaviour, IController {
    public Text nameText;
    public Button closeBtn;
    public Button applyBtn;
    public ChangeCharacterItem changeCharacterItem;
    public Transform itemParent;
    private readonly List<ChangeCharacterItem> changeCharacterItems = new();

    private CharacterInfo curCharacterInfo;
    private bool isFirstTime = true;

    private void Start() {
        applyBtn.interactable = false;
        applyBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            this.SendCommand(new ApplyCharacterCommand(curCharacterInfo));
        });

        closeBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Close);
            this.SendCommand(new ShowPageCommand(UIPageType.HomepageAtisUI));
            this.SendCommand(new HidePageCommand(UIPageType.ChangeCharacterUI));
        });

        this.RegisterEvent<ChangeCharacterEvent>(OnChangeCharacterEvent).UnRegisterWhenGameObjectDestroyed(gameObject);
        this.RegisterEvent<ApplyCharacterEvent>(OnApplyCharacter).UnRegisterWhenGameObjectDestroyed(gameObject);
    }

    private async void OnEnable() {
        this.SendCommand(new ShowPageCommand(UIPageType.LoadingUI, UILevelType.Alart));

        // Addressable第一次加载资源会慢
        if (isFirstTime) {
            isFirstTime = false;
            this.SendCommand(new ShowPageCommand(UIPageType.LoadingUI, UILevelType.Alart));
            Util.DelayExecuteWithSecond(1.4f, () => { this.SendCommand(new HidePageCommand(UIPageType.LoadingUI)); });
        }

        await SetItemContent();
        this.SendCommand(new HidePageCommand(UIPageType.LoadingUI));
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    private async UniTask SetItemContent() {
        Util.DeleteChildren(itemParent);
        changeCharacterItems.Clear();

        // 创建一个任务列表来保存所有的异步任务
        var tasks = new List<UniTask>();
        foreach (var kvp in this.GetModel<ICharacterModel>().CharacterDic) {
            var item = Instantiate(changeCharacterItem);
            item.transform.SetParent(itemParent, false);
            // 添加异步设置内容的任务到任务列表
            tasks.Add(item.SetContent(kvp.Value));
            changeCharacterItems.Add(item);
        }

        // 并行执行所有任务并等待它们全部完成
        await UniTask.WhenAll(tasks);

        curCharacterInfo = this.GetModel<ICharacterModel>().CharacterDic[this.GetModel<IUserModel>().Cid.Value];
        OnChangeCharacterEvent(new ChangeCharacterEvent(curCharacterInfo.cid));
        await this.GetSystem<IIndexCharacterSystem>().MMCharacterStyle.ChangeCharacter(curCharacterInfo.cid);
    }

    private void OnChangeCharacterEvent(ChangeCharacterEvent e) {
        curCharacterInfo = this.GetModel<ICharacterModel>().CharacterDic[e.mCharacterInfo];
        nameText.text = curCharacterInfo.name;
        for (var i = 0; i < changeCharacterItems.Count; i++)
            changeCharacterItems[i].SetSelectState(changeCharacterItems[i].characterInfo.cid == curCharacterInfo.cid);

        if (curCharacterInfo.cid == this.GetModel<IUserModel>().Cid.Value)
            applyBtn.interactable = false;
        else
            applyBtn.interactable = true;
    }

    private void OnApplyCharacter(ApplyCharacterEvent e) {
        applyBtn.interactable = false;
    }
}
