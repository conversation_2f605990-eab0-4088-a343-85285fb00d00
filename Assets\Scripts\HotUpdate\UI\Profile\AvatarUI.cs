﻿using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using QFramework;
using UnityEngine;
using UnityEngine.ResourceManagement.AsyncOperations;
using UnityEngine.UI;
using Utils;

public class AvatarUI : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IController {
    public Image curAvatar;
    public Text curAvatarName;
    public Button applyBtn;
    public AvatarItem avatarItem;
    public Transform avatarItemParent;
    public Button closeBtn;

    private IAvatarModel avatarModel;
    private int curAid;

    private void Start() {
        applyBtn.interactable = false;
        applyBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            this.SendCommand(new ApplyAvatarCommand(curAid));
            applyBtn.interactable = false;
        });

        this.RegisterEvent<UpdataAvatarUIEvent>(OnUpdataAvatarUIEvent).UnRegisterWhenGameObjectDestroyed(gameObject);
    }

    private async void OnEnable() {
        avatarModel = this.GetModel<IAvatarModel>();
        curAid = this.GetModel<IUserModel>().Aid.Value;
        await this.GetSystem<IDataParseSystem>().ParseAvatarRes();
        await UpdateUI();
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    private async UniTask UpdateUI() {
        var obj = await this.GetSystem<IAddressableSystem>().LoadAssetAsync<Sprite>(Util.GetAvatarUrl(curAid));
        if (obj.Status == AsyncOperationStatus.Succeeded) curAvatar.sprite = Instantiate(obj.Result, transform, false);

        curAvatarName.text = avatarModel.AvatarDic[curAid].name;
        Util.DeleteChildren(avatarItemParent);
        // 创建一个任务列表来保存所有的异步任务
        var tasks = new List<UniTask>();
        foreach (var kvp in avatarModel.AvatarDic) {
            var item = Instantiate(avatarItem);
            item.transform.SetParent(avatarItemParent, false);
            tasks.Add(item.SetContent(kvp.Value));
        }

        // 并行执行所有任务并等待它们全部完成
        await UniTask.WhenAll(tasks);
    }

    private async void OnUpdataAvatarUIEvent(UpdataAvatarUIEvent e) {
        applyBtn.interactable = e.aid != this.GetModel<IUserModel>().Aid.Value;

        var obj = await this.GetSystem<IAddressableSystem>().LoadAssetAsync<Sprite>(Util.GetAvatarUrl(e.aid));
        if (obj.Status == AsyncOperationStatus.Succeeded) curAvatar.sprite = Instantiate(obj.Result, transform, false);
        curAvatarName.text = avatarModel.AvatarDic[e.aid].name;
        curAid = e.aid;
    }
}
