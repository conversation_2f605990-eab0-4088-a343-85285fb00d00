﻿using Builtin.Utility;
using QFramework;
using UnityEngine;

public class NetworkController : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IController {
    public ServerTypeAot serverType;

    [SerializeField] public string host;

    private void Awake() {
        this.RegisterEvent<InitNetworkEvent>(OnInitNetwork).UnRegisterWhenGameObjectDestroyed(gameObject);
        DontDestroyOnLoad(gameObject);
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    private void OnInitNetwork(InitNetworkEvent obj) {
        this.GetSystem<INetworkSystem>().ServerType = (ServerTypeAot)PlayerPrefs.GetInt(PlayerPrefKeys.ServerType);
        this.GetSystem<INetworkSystem>().HttpBaseUrl =
            UtilAot.GetServerBaseUrl(this.GetSystem<INetworkSystem>().ServerType);
        host = this.GetSystem<INetworkSystem>().HttpBaseUrl;
    }
}
