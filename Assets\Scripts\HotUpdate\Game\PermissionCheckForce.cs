﻿using System;
using System.Collections;
using UnityEngine;
using UnityEngine.Android;
using QFramework;

public class PermissionCheckForce : Mono<PERSON><PERSON>aviour, IController {
    private bool isInternetCanUse;

    private string text;

    private Action _onCallback;

    private void Awake() {
        this.RegisterEvent<PermissionCheckEvent>(OnPermissionCheck).UnRegisterWhenGameObjectDestroyed(gameObject);
    }

    private void OnPermissionCheck(PermissionCheckEvent e)
    {
        _onCallback = e.m_action;
        // 检查网络权限
        var isInternetConnect = CheckInternetConnection();
        isInternetCanUse = isInternetConnect;

        // 检查并请求权限
        CheckAndRequestPermissions();
    }

    private bool CheckInternetConnection()
    {
        if (Application.internetReachability == NetworkReachability.NotReachable)
        {
            Debug.Log("没有网络连接");
            return false;
        }

        if (Application.internetReachability == NetworkReachability.ReachableViaCarrierDataNetwork)
        {
            Debug.Log("通过移动数据网络连接");
            return true;
        }

        if (Application.internetReachability == NetworkReachability.ReachableViaLocalAreaNetwork)
        {
            Debug.Log("通过WiFi连接");
            return true;
        }

        Debug.LogError("网络异常");
        return false;
    }

    private void CheckAndRequestPermissions()
    {
        // 检查位置权限
        if (!Permission.HasUserAuthorizedPermission(Permission.FineLocation))
        {
            Permission.RequestUserPermission(Permission.FineLocation);
        }

        if (!Permission.HasUserAuthorizedPermission(Permission.CoarseLocation))
        {
            Permission.RequestUserPermission(Permission.CoarseLocation);
        }

        // 检查麦克风权限
        if (!Permission.HasUserAuthorizedPermission(Permission.Microphone))
        {
            Permission.RequestUserPermission(Permission.Microphone);
        }

        // 检查摄像头权限
        if (!Permission.HasUserAuthorizedPermission(Permission.Camera))
        {
            Permission.RequestUserPermission(Permission.Camera);
        }

        // 检查所有权限是否已授予
        StartCoroutine(CheckPermissionsCoroutine());
    }

    private IEnumerator CheckPermissionsCoroutine()
    {
        yield return new WaitForSeconds(1); // 等待权限请求完成

        if (!Permission.HasUserAuthorizedPermission(Permission.FineLocation) ||
            !Permission.HasUserAuthorizedPermission(Permission.CoarseLocation) ||
            !Permission.HasUserAuthorizedPermission(Permission.Microphone) ||
            !Permission.HasUserAuthorizedPermission(Permission.Camera) ||
            !isInternetCanUse)
        {
            // 如果缺少权限或网络，显示提示并退出应用
            string message = "缺少必要权限或网络连接，请确保授予以下权限并连接网络：\n";
            if (!Permission.HasUserAuthorizedPermission(Permission.FineLocation)) message += "- 精确位置\n";
            if (!Permission.HasUserAuthorizedPermission(Permission.CoarseLocation)) message += "- 位置\n";
            if (!Permission.HasUserAuthorizedPermission(Permission.Microphone)) message += "- 麦克风\n";
            if (!Permission.HasUserAuthorizedPermission(Permission.Camera)) message += "- 摄像头\n";
            if (!isInternetCanUse) message += "- 网络连接\n";

            var info = new InfoConfirmInfo("权限异常提示",
                message,
                Application.Quit, type: ConfirmAlertType.Single);
            this.SendCommand(new ShowPageCommand(UIPageType.InfoConfirmAlert, UILevelType.Alart, info));
        }
        else
        {
            // 所有权限已授予，可以正常使用应用
            Debug.Log("所有权限已授予");
            _onCallback?.Invoke();
        }
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }
}
