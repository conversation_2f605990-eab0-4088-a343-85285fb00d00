using QFramework;
using UnityEngine;

public class InitGameConfigState : AbstractState<LaunchStates, Launch>, IController {
    public InitGameConfigState(FSM<LaunchStates> fsm, Launch target) : base(fsm, target) {
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    protected override async void OnEnter() {
        Screen.sleepTimeout = SleepTimeout.NeverSleep;
        InitSettingsInfo();

        await this.GetSystem<II18NSystem>().InitTranslation();
        ChangeState();
    }

    private void ChangeState() {
        mFSM.ChangeState(LaunchStates.EnterGame);
    }

    private void InitSettingsInfo() {
        this.SendCommand<SavaSettingsCommand>();
    }
}
