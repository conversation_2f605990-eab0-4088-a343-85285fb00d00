﻿using System.Collections;
using QFramework;
using UnityEngine;
using UnityEngine.UI;

public class CountDownTimer : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IController {
    public Text text;

    // 运行状态
    private Coroutine _countdownCoroutine;

    private bool _isCoroutineCompleted = true;
    private float _remainingTime;
    private IChatModel chatModel;

    private void Start() {
        // 初始化模型
        chatModel = this.GetModel<IChatModel>();
        this.RegisterEvent<ChatOverEvent>(ChatOver).UnRegisterWhenGameObjectDestroyed(gameObject);
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    /// <summary>
    ///     开始倒计时
    /// </summary>
    /// <param name="duration">总时长（秒）</param>
    /// <param name="callback">倒计时完成回调</param>
    public void StartCountdown(int duration) {
        // 停止正在进行的倒计时
        if (!_isCoroutineCompleted && _countdownCoroutine != null) {
            Debug.Log("StopCountdown: " + _isCoroutineCompleted);
            StopCoroutine(_countdownCoroutine);
            _isCoroutineCompleted = true;
        }
        // 初始化参数
        _remainingTime = duration;
        // 启动新协程
        _countdownCoroutine = StartCoroutine(CountdownRoutine());
    }

    /// <summary>
    ///     倒计时协程
    /// </summary>
    private IEnumerator CountdownRoutine() {
        Debug.Log("CountdownRoutine: " + _isCoroutineCompleted);
        if (!_isCoroutineCompleted) yield break;
        _isCoroutineCompleted = false;

        chatModel.IsTimeout = false;
        UpdateTimerDisplay((int)_remainingTime);

        while (_remainingTime > 0 && !chatModel.IsQuit) {
            if (!chatModel.Pause.Value) {
                _remainingTime -= Time.deltaTime;
                _remainingTime = Mathf.Max(_remainingTime, 0);
                UpdateTimerDisplay((int)_remainingTime);
            }
            yield return null;
        }
        chatModel.IsTimeout = true;

        // 仅在正常结束时触发回调
        if (!chatModel.IsQuit && _remainingTime <= 0) this.SendCommand<ChatTimeOutCommand>();
        _isCoroutineCompleted = true;
    }

    /// <summary>
    ///     强制停止倒计时
    /// </summary>
    private void ChatOver(ChatOverEvent e) {
        if (!_isCoroutineCompleted && _countdownCoroutine != null) {
            StopCoroutine(_countdownCoroutine);
            _isCoroutineCompleted = true;
        }
    }

    private void UpdateTimerDisplay(int time) {
        var minutes = time / 60;
        var seconds = time % 60;
        text.text = $"{minutes:D1}:{seconds:D2}";
    }
}
