﻿using System;
using System.Collections.Generic;
using System.IO;
using Cysharp.Threading.Tasks;
using DTC;
using Newtonsoft.Json;
using QFramework;
using UnityEngine;
using UnityEngine.SceneManagement;
using Utils;

public class TaskQuitCommand : AbstractCommand {
    protected override void OnExecute() {
        this.SendEvent(new TaskQuitEvent());
    }
}

public class TaskPauseResumeCommand : AbstractCommand {
    private IChatModel chatModel;
    public bool m_pause;

    public TaskPauseResumeCommand(bool pause) {
        m_pause = pause;
    }

    protected override void OnExecute() {
        chatModel = this.GetModel<IChatModel>();
        chatModel.ArchiveInfoDic.Value.EventTimeStamp.Add((
            m_pause ? ChatStates.Pause.ToString() : ChatStates.Resume.ToString(),
            Time.time - chatModel.OneChatStartTimestamp));
        this.SendEvent(new TaskPauseResumeEvent(m_pause));
    }
}

public class TaskStartCommand : AbstractCommand {
    private ITaskModel taskModel;
    private IUserModel userModel;
    private IChatModel chatModel;
    private IEegModel eegModel;

    protected override async void OnExecute() {
        chatModel = this.GetModel<IChatModel>();
        taskModel = this.GetModel<ITaskModel>();
        eegModel = this.GetModel<IEegModel>();
        userModel = this.GetModel<IUserModel>();

        chatModel.Pause.Value = false;
        chatModel.IsTimeout = false;
        chatModel.IsQuit = false;

        // 重置聊天历史记录 TDOO
        taskModel.taskMetrics = new TaskMetrics();
        chatModel.m_HistoryDataList.Clear();
        chatModel.ArchiveInfoDic.Value.EventTimeStamp = new List<(string, float)>();

        var userInfo = userModel.GetUserInfo();
        var taskInfo = taskModel.taskInfo;
        chatModel.ArchiveInfoDic.Value.CalibrationEEGDataFilePath =
            PlayerPrefs.GetString(PrefKeys.CalibrationEEGDataFilePath);

        chatModel.ArchiveInfoDic.Value.UserDic = userInfo;
        chatModel.ArchiveInfoDic.Value.TaskDic = taskInfo;

        var taskName = taskInfo.name;
        var chatTime = taskInfo.time;
        Debug.Log($"设定谈话时间：{chatTime}");

        // EEG设备蓝牙信息
        var bluetoothAddr = "bluetoothAddr";
        var bluetoothName = "bluetoothName";
        if (eegModel.isActive.Value && eegModel.eegStatus.Value == EegStatusEnum.Connected) {
            bluetoothAddr = BluetoothManager.Instance.ConnectedDeviceAddress;
            bluetoothName = BluetoothManager.Instance.FoundDeviceDic[bluetoothAddr].Name;
        }

        chatModel.ArchiveInfoDic.Value.StartTime = Util.NowTime();
        chatModel.ArchiveInfoDic.Value.DeviceInfo = bluetoothName + "：" + bluetoothAddr;

        // 对话任务开始时间
        chatModel.OneChatStartTimestamp = Time.time;
        chatModel.lastUserSpeakEndTime = Time.time;
        Debug.Log($"任务开始时间({taskName}): " + Util.NowTime());

        chatModel.ArchiveInfoDic.Value.EventTimeStamp.Add((ChatStates.Start.ToString(),
            Time.time - chatModel.OneChatStartTimestamp));

        chatModel.FileName.Value = Util.NowTime();

        this.SendEvent(new TaskStartEvent(taskName, chatTime));
    }
}

// 后续这里可能需要传递任务类型
public class ExitTaskCommand : AbstractCommand {
    protected override void OnExecute() {
        this.SendEvent(new ExitTaskEvent());
    }
}

public class UpdateTaskCommand : AbstractCommand {
    private Action m_Action;
    public ITaskModel m_taskModel;
    public string m_today;

    public UpdateTaskCommand(Action action) {
        m_Action = action;
    }

    protected override async void OnExecute() {
        await LoadTaskData();
    }

    private async UniTask LoadTaskData() {
        Debug.Log("LoadTaskData");
        // 请求任务数据
        this.SendCommand(new ShowPageCommand(UIPageType.LoadingUI, UILevelType.Alart));
        var result = await this.GetSystem<INetworkSystem>().PostJson<TaskInfoResponse>(
            RequestUrl.getTaskUrl,
            token: this.GetModel<IUserModel>().Token.Value);

        this.SendCommand(new HidePageCommand(UIPageType.LoadingUI));

        if (result.IsSuccess) {
            // 加载网络数据
            this.GetSystem<IDataParseSystem>().ParseTaskData(result.Data);
            m_Action.Invoke();
        } else {
            if ((int)result.StatusCode == 404) {
                var alertInfo = new WarningAlertInfo("今日任务已全部完成");
                this.SendEvent(new ShowPageEvent(UIPageType.WarningAlert, UILevelType.Alart, alertInfo));
                if (SceneManager.GetActiveScene().name != SceneID.Index.ToString())
                    this.SendCommand(new LoadSceneCommand(ScenePath.Index));
            } else {
                Debug.LogError("获取任务失败：" + result.StatusCode + " " + result.Data + " " + result.ErrorMessage);
                var code = (int)result.StatusCode;
                if (code is 401 or 204 or 203) {
                } else {
                    var info = new InfoConfirmInfo("", "服务器出错啦！\n请联系管理员！\n点击确定后，将退出APP。",
                        Application.Quit
                        , type: ConfirmAlertType.Single);
                    this.SendEvent(new ShowPageEvent(UIPageType.InfoConfirmAlert, UILevelType.Alart, info));
                }
            }
        }
    }
}

public class SaveDataAndUploadCommand : AbstractCommand {
    private IChatModel chatModel;
    private IEegModel eegModel;
    private ChatOverStates m_chatOverStates;
    private ITaskModel taskModel;

    public SaveDataAndUploadCommand(ChatOverStates chatOverStates) {
        m_chatOverStates = chatOverStates;
        Debug.Log($"m_chatOverStates: {m_chatOverStates}");
    }

    protected override async void OnExecute() {
        Debug.Log("SaveDataAndUploadCommand");
        chatModel = this.GetModel<IChatModel>();
        taskModel = this.GetModel<ITaskModel>();
        eegModel = this.GetModel<IEegModel>();

        await SaveDataAndUpload();
    }

    /// <summary>
    ///     保存聊天到json文件
    /// </summary>
    private async UniTask SaveDataAndUpload() {
        var taskInfo = $"{taskModel.taskInfo.name}-{taskModel.taskInfo.id}-{taskModel.taskInfo.tid}";

        Debug.Log("SaveDataAndUpload");
        // 将TextHistory赋值
        chatModel.ArchiveInfoDic.Value.TextHistory = chatModel.m_HistoryDataList;
        //保存
        var archivePath = Path.Combine(Util.ThemeDir, $"chatHistory.{chatModel.ArchiveInfoDic.Value.StartTime}.json");
        Debug.Log($"保存聊天到json文件:{archivePath}");
        Json.JsonDump(archivePath, chatModel.ArchiveInfoDic.Value);

        // 2. 上传 任务完成标识
        if (m_chatOverStates == ChatOverStates.Normal) {
            taskInfo = $"{taskModel.taskInfo.name}-{taskModel.taskInfo.id}-{taskModel.taskInfo.tid}-completed";
            await SendTaskIsComplete();
            // 3. 上传 任务数据
            await UploadData(taskInfo);
        } else if (m_chatOverStates == ChatOverStates.Error) {
            // 3. 上传 任务数据
            await UploadData(taskInfo);
            // 4. 退出，回到主页面
            this.SendCommand(new TaskQuitCommand());
        }
    }

    private async UniTask SendTaskIsComplete() {
        Debug.Log("2. SendTaskIsComplete");
        var requestData = new SetTaskIsCompleteRequest { id = taskModel.taskInfo.id };

        var result = await this.GetSystem<INetworkSystem>().PostJson<object>(
            RequestUrl.sendTaskIsComplete,
            requestData,
            this.GetModel<IUserModel>().Token.Value);

        if (result.IsSuccess) {
            Debug.Log("Show TaskResultUI");
            this.SendCommand(new ShowPageCommand(UIPageType.TaskResultUI));
        } else {
            Debug.LogError("sendTaskIsComplete: 发送任务完成标识失败 " + result.StatusCode +
                           " " + result.Data + " " + result.ErrorMessage);
            this.SendCommand(new TaskQuitCommand());
            // this.SendCommand(new LoadSceneCommand(ScenePath.Index)); // 切换场景到主页面
        }
    }

    private async UniTask UploadData(string taskInfo) {
        Debug.Log("3. UploadData");

        // 1. 上传聊天记录
        // 格式化保存
        var settings = new JsonSerializerSettings {
            NullValueHandling = NullValueHandling.Ignore,
            DefaultValueHandling = DefaultValueHandling.Ignore
        };

        var requestDataChatHistory = new ChatHistoryRequest {
            uid = this.GetModel<IUserModel>().Uid.Value,
            id = taskModel.taskInfo.id,
            fileData = JsonConvert.SerializeObject(chatModel.ArchiveInfoDic.Value, settings),
            exception = m_chatOverStates == ChatOverStates.Error
        };

        var resultChatHistory = await this.GetSystem<INetworkSystem>().PostJson<object>(
            RequestUrl.uploadChatHistory,
            requestDataChatHistory,
            this.GetModel<IUserModel>().Token.Value
        );
        if (resultChatHistory.IsSuccess)
            Debug.Log("上传聊天历史记录成功");
        else
            Debug.LogError("uploadChatHistory: 上传聊天历史记录失败 " + resultChatHistory.StatusCode +
                           " " + resultChatHistory.Data + " " + resultChatHistory.ErrorMessage);
        // TODO: 接收到总结数据？？

        // 2. 上传 EEG数据
        if (eegModel.EEGPath.IsNotNullAndEmpty() && eegModel.isActive.Value) {
            var result = await this.GetSystem<INetworkSystem>().UploadFileAsync(
                eegModel.EEGPath,
                RequestUrl.UploadEEGUrl,
                mimeType: "text/plain",
                token: this.GetModel<IUserModel>().Token.Value);

            if (result.Success)
                Debug.Log("EEG 数据 上传完毕");
            else
                Debug.LogError($"UploadEEGUrl 上传脑电数据记录失败, {result.ErrorMessage} {result.StatusCode}");
        }

        // 3. 上传日志
        this.SendCommand(new UploadLogCommand(taskInfo));
    }
}

public class AvatarSpeakEndStateCommand : AbstractCommand {
    protected override void OnExecute() {
        this.SendEvent(new AvatarSpeakEndEvent());
    }
}

public class UserSpeakEndCommand : AbstractCommand {
    protected override void OnExecute() {
        this.SendEvent(new UserSpeakEndEvent());
    }
}

public class ChatOverCommand : AbstractCommand {
    public ChatOverStates m_chatOverStates;

    public ChatOverCommand(ChatOverStates chatOverStates) {
        m_chatOverStates = chatOverStates;
    }

    protected override void OnExecute() {
        this.SendEvent(new ChatOverEvent(m_chatOverStates));
    }
}

public class ChatTimeOutCommand : AbstractCommand {
    protected override void OnExecute() {
        this.SendEvent(new ChatTimeOutEvent());
    }
}

public class ForceStopTaskCoroutineCommand : AbstractCommand {
    protected override void OnExecute() {
        this.SendEvent(new ForceStopTaskCoroutineEvent());
    }
}
