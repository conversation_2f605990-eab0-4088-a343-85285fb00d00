﻿using QFramework;
using UnityEngine;
using Utils;

public enum LaunchStates {
    InitNetwork,
    InitConfig,
    PrepareUI,
    InitGameConfig,
    AssetsUpdate,
    EnterGame,
    ExitGameState,
    InitService
}

public class Launch : MonoBehaviour, IController {
    public FSM<LaunchStates> FSM = new();

    private void Awake() {
        DontDestroyOnLoad(gameObject);
    }

    private void Start() {
        Util.InitPath(); // 初始化路径
        Screen.sleepTimeout = SleepTimeout.NeverSleep; // 设置屏幕常亮
        // 其他模块需要在Awake中注册事件
        FSM.AddState(LaunchStates.InitNetwork, new InitNetworkState(FSM, this));
        FSM.AddState(LaunchStates.PrepareUI, new PrepareUIState(FSM, this));
        FSM.AddState(LaunchStates.AssetsUpdate, new AssetsUpdateState(FSM, this));
        FSM.AddState(LaunchStates.InitConfig, new InitConfigState(FSM, this));
        FSM.AddState(LaunchStates.InitService, new InitServiceState(FSM, this));
        FSM.AddState(LaunchStates.InitGameConfig, new InitGameConfigState(FSM, this));
        FSM.AddState(LaunchStates.EnterGame, new EnterGameState(FSM, this));
        FSM.AddState(LaunchStates.ExitGameState, new ExitGameState(FSM, this));

        FSM.StartState(LaunchStates.InitNetwork);
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }
}
