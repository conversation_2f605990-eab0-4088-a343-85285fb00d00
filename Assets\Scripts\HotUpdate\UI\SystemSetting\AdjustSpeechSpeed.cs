using System;
using Newtonsoft.Json;
using QFramework;
using UnityEngine;
using UnityEngine.UI;
using Utils;

public class AdjustSpeechSpeed : MonoBehaviour, IController {
    private const string Msg = "这是{0}倍语速的测试结果。";
    public Button testBtn;
    public Button closeBtn;
    public Button confirmBtn;
    public Slider speechRateSlider;
    public Text speechSpeedText;
    private IAudioManagerSystem _audioManager;
    private IWebSocketSystem _wsManager;

    private IConfigModel m_ConfigModel;

    private float speechRate; // 内部临时存储变量，主要是 testBtn 时，需要用到 m_ConfigModel.VoiceService.Rate

    public void Awake() {
        m_ConfigModel = this.GetModel<IConfigModel>();
        _wsManager = this.GetSystem<IWebSocketSystem>();
        _audioManager = this.GetSystem<IAudioManagerSystem>();

        closeBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Close);
            // TODO： 判断是否相等，不相等则弹窗提示
            m_ConfigModel.Voice.Rate.Value = speechRate;
            _wsManager.SendMessage(JsonConvert.SerializeObject(new { type = "abort" }));
            _audioManager.ResetPlayback();
            _audioManager.audioSource.Stop();
            _wsManager.Disconnect();
            gameObject.SetActiveFast(false);
        });

        testBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);

            var gender = this.GetModel<ICharacterModel>().CharacterDic[this.GetModel<IUserModel>().Cid.Value]
                .gender;
            var rate = this.GetModel<IConfigModel>().Voice.Rate.Value; // 语速 0.4-1.0
            var text = string.Format(Msg, DecimalToChinese(speechRateSlider.value / 10f));

            var ttsMsg = new {
                type = "atis",
                state = "tts",
                voice_config = new {
                    rate, // 语速 0.4-1.0
                    gender // boy girl
                },
                text
            };

            var json = JsonConvert.SerializeObject(ttsMsg);
            _wsManager.SendMessage(json);
        });

        confirmBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            speechRate = m_ConfigModel.Voice.Rate.Value;
            var alertInfo = new WarningAlertInfo(
                $"虚拟人语速修改成功！\n语速：{m_ConfigModel.Voice.Rate.Value:F1}倍");
            this.SendCommand(new ShowPageCommand(UIPageType.WarningAlert, UILevelType.Alart, alertInfo));
        });

        speechRateSlider.onValueChanged.AddListener(v => {
            var value = Mathf.Clamp(v, 4, 10);

            speechRateSlider.value = value;

            var rate = value / 10;
            speechSpeedText.text = rate.ToString();
            m_ConfigModel.Voice.Rate.Value = rate;
        });
    }

    public void OnEnable() {
        Debug.Log("AdjustSpeechSpeed: OnEnable");
        speechRate = m_ConfigModel.Voice.Rate.Value;
        speechSpeedText.text = speechRate.ToString();
        speechRateSlider.value = speechRate * 10;

        _audioManager.audioSource.Play();
        var wsUrl = this.GetSystem<IWebSocketSystem>().GetWebSocketUrl();
        _wsManager.Connect(wsUrl);
    }

    public void OnDisable() {
        Debug.Log("AdjustSpeechSpeed: OnDisable");
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    private void ExceptionCallback() {
        var alertInfo = new WarningAlertInfo("不好意思，语音合成失败。\n请稍后再次测试。");
        this.SendCommand(new ShowPageCommand(UIPageType.WarningAlert, UILevelType.Alart, alertInfo));
    }

    private static string DecimalToChinese(double number) {
        if (number < 0.4 || number > 1.0) throw new ArgumentOutOfRangeException("number", "数字必须在0.4到1.0之间");

        string[] units = { "零", "一", "二", "三", "四", "五", "六", "七", "八", "九" };
        string[] decimalUnits = { "点", "零" };

        // 分离整数部分和小数部分
        var integerPart = (int)number;
        var decimalPart = (int)((number - integerPart) * 10);

        var result = "";

        // 处理整数部分
        if (integerPart > 0)
            result += units[integerPart];
        else
            result += "零";

        // 处理小数部分
        result += decimalUnits[0]; // 添加“点”
        result += units[decimalPart];

        return result;
    }
}
