﻿using QFramework;
using UnityEngine;
using Utils;

public class ExitGameState : AbstractState<LaunchStates, Launch>, IController {
    public ExitGameState(FSM<LaunchStates> fsm, Launch target) : base(fsm, target) {
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }


    protected override void OnEnter() {
        var info = new InfoConfirmInfo(
            content: "游戏初始化失败, 是否退出游戏?",
            success: Util.AppQuit, type: ConfirmAlertType.Double);
        this.SendCommand(new ShowPageCommand(UIPageType.InfoConfirmAlert, UILevelType.Alart, info));
    }
}
