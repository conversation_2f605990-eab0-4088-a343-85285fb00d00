﻿using QFramework;

public interface IGameModel : IModel {
    public BindableProperty<bool> SceneLoaded { get; }
    public BindableProperty<bool> SceneLoading { get; }
    public BindableProperty<string> LoadingTargetSceneID { get; }
}

public class GameModel : AbstractModel, IGameModel {
    public BindableProperty<bool> SceneLoaded { get; } = new();
    public BindableProperty<bool> SceneLoading { get; } = new();
    public BindableProperty<string> LoadingTargetSceneID { get; } = new();

    protected override void OnInit() {
        SceneLoaded.Value = false;
    }
}
