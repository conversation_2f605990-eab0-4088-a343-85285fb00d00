﻿using System;
using System.Collections.Generic;
using QFramework;
using UnityEngine;
using UnityEngine.UI;
using Utils;
using static UnityEngine.UI.Dropdown;

public class SettingsUI : MonoBehaviour, IController {
    public Button closeBtn;
    public Button exitBtn;
    public Dropdown languageDropdown;
    public Slider musicSlider;
    public Slider soundSlider;
    public Slider vibrationSlider;
    public Button audioVisualDemoBtn;
    public Button adjustSpeechSpeedBtn;
    private readonly List<OptionData> languageOptions = new();
    private readonly List<string> languageOptionsList = new();

    private bool isInit;

    private void Start() {
        closeBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Close);
            SaveSettings();
            gameObject.SetActiveFast(false);
        });

        exitBtn.onClick.AddListener(() => {
            // 目前为隐藏状态
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            this.GetModel<IUserModel>().RememberPassword.Value = 0; // 退出不注销账号
            this.SendCommand(new LoadSceneCommand(ScenePath.Login));
        });

        audioVisualDemoBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            this.SendCommand(new ShowPageCommand(UIPageType.AudioVisualDemo));
        });

        adjustSpeechSpeedBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            this.SendCommand(new ShowPageCommand(UIPageType.AdjustSpeechSpeedUI));
        });

        languageOptionsList.Clear();
        var lanMap = this.GetSystem<II18NSystem>().LangMap;
        foreach (var kv in lanMap) languageOptionsList.Add(kv.Key);
        languageDropdown.ClearOptions();
        languageOptions.Clear();
        foreach (var t in languageOptionsList) {
            var optionData = new OptionData();
            optionData.text = t;
            optionData.image = this.GetSystem<II18NSystem>().FlagsDic[t];

            languageOptions.Add(optionData);
        }

        languageDropdown.AddOptions(languageOptions);
        isInit = true;
        UpdateUI();
    }

    private void OnEnable() {
        UpdateUI();
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    private void UpdateUI() {
        if (!isInit) return;

        var info = this.GetModel<ISettingsModel>();
        for (var i = 0; i < languageOptionsList.Count; i++)
            if (info.Language.Value.Equals(this.GetSystem<II18NSystem>().LangMap[languageOptionsList[i]])) {
                languageDropdown.value = i;
                break;
            }

        musicSlider.value = Convert.ToInt32(info.IsOnMusic.Value);
        soundSlider.value = Convert.ToInt32(info.IsOnSound.Value);
        vibrationSlider.value = Convert.ToInt32(info.IsOnVibration.Value);
    }

    private SystemSettingsInfo GetCurrentInfo() {
        var info = new SystemSettingsInfo();
        info.language = this.GetSystem<II18NSystem>().LangMap[languageDropdown.captionText.text];
        info.isOnMusic = Convert.ToBoolean(musicSlider.value);
        info.isOnSound = Convert.ToBoolean(soundSlider.value);
        info.isOnVibration = Convert.ToBoolean(vibrationSlider.value);
        return info;
    }

    /*********     判断信息改变    ***********/
    public void SaveSettings(Action success = null) {
        this.GetModel<ISettingsModel>().SaveSystemInfo(GetCurrentInfo());
        // 切换语言
        this.SendCommand<SavaSettingsCommand>();
        success?.Invoke();
    }
}
