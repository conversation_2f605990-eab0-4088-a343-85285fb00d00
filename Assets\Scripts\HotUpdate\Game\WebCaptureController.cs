﻿using QFramework;
using RenderHeads.Media.AVProMovieCapture;
using UnityEngine;

public class WebCaptureController : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IController {
    public static WebCaptureController manager;
    public GameObject capturePrefab;
    private string capturePrefabPath = "Assets/AA/Prefabs/Utility/Service/WebcamCapturePrefab.prefab";
    private readonly string fileExtension = ".mp4"; // 视频扩展名

    // 视频设置字段
    private readonly string fileName = "CaptureVideo"; // 视频文件名
    private readonly int frameRate = 10; // 视频帧率 30
    private readonly int videoHeight = 240; // 视频高度  240 360
    private readonly int videoWidth = 320; //视频宽度  320 640

    private WebCamTexture webcamTexture;

    private void Awake() {
        this.RegisterEvent<InitWebCaptureEvent>(OnInitWebCapture).UnRegisterWhenGameObjectDestroyed(gameObject);

        if (manager == null) {
            DontDestroyOnLoad(gameObject);
            manager = this;
        } else if (manager != this) {
            Destroy(gameObject);
        }
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    private void OnInitWebCapture(InitWebCaptureEvent e) {
        print("OnInitWebCapture");
        if (Application.platform != RuntimePlatform.Android) {
            e.action?.Invoke();
            return;
        }

        Debug.Log("WebCapture Start");
        // 初始化摄像头
        Debug.Log("WebCamTexture.devices.Length:" + WebCamTexture.devices.Length);
        if (WebCamTexture.devices.Length > 0) {
            // 选择前置摄像头
            WebCamDevice? frontCam = null;
            foreach (var cam in WebCamTexture.devices)
                if (cam.isFrontFacing) {
                    frontCam = cam;
                    break;
                }

            Debug.Log("frontCam.Value:" + frontCam.Value);
            if (frontCam.HasValue) {
                webcamTexture = new WebCamTexture(frontCam.Value.name);
            } else {
                // 如果没有找到前置摄像头，则使用默认找到的摄像头
                webcamTexture = new WebCamTexture();
                Debug.LogWarning("Front webcam device not found.");
            }
            var captureInstance = Instantiate(capturePrefab, transform);
            var captureComponent = captureInstance.GetComponent<CaptureFromWebCamTexture>();
            this.GetSystem<IWebCaptureSystem>().CaptureComponent = captureComponent;
            if (captureComponent == null) {
                Debug.LogError("captureComponent == null");
                Debug.LogError("AVProMovieCapture component not found on the prefab.");
                return;
            }

            // 设置输出路径和文件名
            captureComponent.FilenamePrefix = fileName;
            captureComponent.OutputFolder = CaptureBase.OutputPath.RelativeToPeristentData;
            captureComponent.FilenameExtension = fileExtension;
            captureComponent.FrameRate = frameRate;
            captureComponent.SelectRecordingResolution(videoWidth, videoHeight);
            captureComponent.AppendFilenameTimestamp = false;
            captureComponent.AudioCaptureSource = AudioCaptureSource.Microphone; // 确保视频有声音，且和录音服务不冲突

            var encoderHints = captureComponent.GetEncoderHints();

            // 调整视频编码器提示以减小文件大小
            encoderHints.videoHints.averageBitrate = 1000000; // 设置平均比特率为 1 Mbps
            encoderHints.videoHints.maximumBitrate = 2000000; // 设置最大比特率为 2 Mbps
            encoderHints.videoHints.quality = 0.2f; // 质量设置为中等（0.0 到 1.0 之间，较低的值表示较低的质量）
            encoderHints.videoHints.keyframeInterval = 250; // 每250帧插入一个关键帧
            encoderHints.videoHints.useHardwareEncoding = true; // 启用硬件编码
            // 将修改后的编码器提示应用回 CaptureBase 实例
            captureComponent.SetEncoderHints(encoderHints);

            captureComponent.WebCamTexture = webcamTexture; // 将摄像头纹理设置给CaptureFromWebCamTexture
            print("OnInitWebCapture over");
            e.action?.Invoke();
        } else {
            e.action?.Invoke();
            Debug.LogError("No webcam devices found.");
        }
    }
}
