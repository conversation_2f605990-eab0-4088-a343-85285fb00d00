using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class Item : MonoBehaviour {
    public int index;
    public ListView m_parent;
    public object data;
    public Dictionary<string, Transform> m_childName;

    public void RefData() {
        index = 0;
        data = null;
        m_parent = null;
        m_childName = new Dictionary<string, Transform>();
    }
}

public struct ItemDate {
    public int index;
    public object data;
    public ListView m_parent;
}

public class ListView : ScrollRect {
    private float _m_down;

    private float _m_left;

    private float _m_top;

    private Vector2 _sizeData;

    private List<RectTransform> comList;
    private int endIndex;
    private List<ItemDate> m_datas;
    private float m_height;
    private RectTransform m_Skin;

    private float m_width;
    private RectTransform rectTr;
    private int startIndex;

    public float m_top {
        set => _m_top = value;
    }

    public float m_down {
        set => _m_down = value;
    }

    public float m_left {
        set => _m_left = value;
    }

    public float Spacing { get; set; }

    public Vector2 sizeData => _sizeData;

    public RectTransform setSkin {
        set => m_Skin = value;
    }

    protected override void Awake() {
        base.Awake();
        rectTr = GetComponent<RectTransform>();
        comList = new List<RectTransform>();
        m_datas = new List<ItemDate>();
        startIndex = 0;
        Spacing = 50;
        m_width = rectTr.sizeDelta.x;
        m_height = rectTr.sizeDelta.y;
        OnChangeViewWH += ChangeViewWH;
        OnUpdateItem += UpdateChild;
        onValueChanged.AddListener(OnChange);
    }

    protected override void Start() {
        base.Start();
    }

    private void Update() {
        if (m_width != rectTr.sizeDelta.x || m_height != rectTr.sizeDelta.y) {
            m_width = rectTr.sizeDelta.x;
            m_height = rectTr.sizeDelta.y;
            OnChangeViewWH?.Invoke();
        }
    }

    /// <summary>
    ///     宽高改变时触发
    /// </summary>
    public event Action OnChangeViewWH;

    /// <summary>
    ///     数据更新时调用
    /// </summary>
    public event Action<Item> OnUpdateItem;

    public void setData<T>(List<T> datas) {
        if (m_Skin == null) {
            Debug.LogError("Item皮肤没有设置");
            return;
        }

        m_datas.Clear();
        content.localPosition = new Vector3(content.localPosition.x, 0, 0);
        startIndex = 0;
        var len = datas.Count;
        _sizeData = new Vector2(m_Skin.sizeDelta.x,
            m_Skin.sizeDelta.y * len + Spacing * len - Spacing + _m_down + _m_top);

        for (var i = 0; i < datas.Count; i++) {
            var data = new ItemDate { index = i, data = datas[i], m_parent = this };
            m_datas.Add(data);
        }

        // 确保旧的 RectTransform 被删除
        var itemsToRemove = comList.Count - len;
        if (itemsToRemove > 0)
            for (var i = 0; i < itemsToRemove; i++) {
                Destroy(comList[comList.Count - 1].gameObject);
                comList.RemoveAt(comList.Count - 1);
            }

        ChangeViewWH();
    }

    private Dictionary<string, Transform> setUIChildName(Transform skin) {
        var map = new Dictionary<string, Transform>();

        for (var i = 0; i < skin.childCount; i++) {
            var childobj = skin.GetChild(i);
            map[childobj.gameObject.name] = childobj;

            if (childobj.childCount > 0) {
                var childMap = setUIChildName(childobj);
                foreach (var item in childMap) map[item.Key] = item.Value;
            }
        }

        return map;
    }

    public void ChangeViewWH() {
        var colunm = Mathf.CeilToInt((m_height + Spacing) / (m_Skin.sizeDelta.y + Spacing));
        endIndex = startIndex + colunm;

        if (comList.Count > colunm + 1 && colunm > 0) {
            for (var s = colunm + 1; s < comList.Count; s++) {
                Destroy(comList[s].gameObject);
                comList.RemoveAt(s);
            }

            return;
        }

        //if (colunm <= m_datas.Count)
        //{
        for (var i = comList.Count; i <= colunm && i < m_datas.Count; i++) {
            var skin = Instantiate(m_Skin);
            skin.gameObject.AddComponent<Item>();
            comList.Add(skin);
            skin.SetParent(content, false);
        }
        //}

        UpdateItem();
        content.sizeDelta = new Vector2(content.sizeDelta.x, _sizeData.y);
    }

    private void UpdateItem() {
        for (var i = 0; i < comList.Count; i++) {
            //if (i >= m_datas.Count)
            //{
            //    Destroy(comList[m_datas.Count].gameObject);
            //    comList.RemoveAt(m_datas.Count);
            //}
            //else
            //{
            comList[i].localPosition = new Vector3(_m_left, -(_m_top + i * (m_Skin.sizeDelta.y + Spacing)), 0);
            UpdateView(comList[i], i);
            //}
        }
    }

    private void UpdateView(RectTransform _comList, int i) {
        var data = _comList.gameObject.GetComponent<Item>();
        data.index = m_datas[i].index;
        data.data = m_datas[i].data;
        data.m_parent = m_datas[i].m_parent;
        data.m_childName = setUIChildName(_comList);
        OnUpdateItem?.Invoke(data);
    }

    private void OnChange(Vector2 pos) {
        if (m_Skin == null) return;
        var colunm = endIndex - startIndex;
        var curindex = Mathf.FloorToInt(content.localPosition.y / (m_Skin.sizeDelta.y + Spacing));

        if (curindex > startIndex && endIndex + 1 < m_datas.Count) {
            for (var i = 0; i < comList.Count; i++) {
                var data = comList[i].gameObject.GetComponent<Item>();
                if (data.index == startIndex) {
                    comList[i].localPosition = new Vector3(_m_left,
                        -_m_top - (endIndex + 1) * (m_Skin.sizeDelta.y + Spacing), 0);
                    UpdateView(comList[i], endIndex + 1);
                    break;
                }
            }

            startIndex++;
            endIndex = startIndex + colunm;
        } else if (curindex < startIndex && startIndex - 1 >= 0 && pos.y < 1) {
            for (var i = 0; i < comList.Count; i++) {
                var data = comList[i].gameObject.GetComponent<Item>();
                if (data.index == endIndex) {
                    comList[i].localPosition = new Vector3(_m_left,
                        -_m_top - (startIndex - 1) * (m_Skin.sizeDelta.y + Spacing), 0);
                    UpdateView(comList[i], startIndex - 1);
                    break;
                }
            }

            startIndex--;
            endIndex = startIndex + colunm;
        }
    }

    public virtual void UpdateChild(Item data) {
    }
}
