﻿using System.Collections.Generic;
using QFramework;

public class AvatarInfo {
    public int aid;
    public bool isHas;
    public string name;
    public int star;
}

public interface IAvatarModel : IModel {
    public Dictionary<int, AvatarInfo> AvatarDic { get; set; }
}

public class AvatarModel : AbstractModel, IAvatarModel {
    public Dictionary<int, AvatarInfo> AvatarDic { get; set; } = new();

    protected override void OnInit() {
    }
}
