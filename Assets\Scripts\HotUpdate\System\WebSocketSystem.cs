using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;
using Best.HTTP;
using Best.HTTP.Shared.PlatformSupport.Memory;
using Best.WebSockets;
using Best.WebSockets.Implementations;
using QFramework;
using UnityEngine;
using Builtin.Utility;
using Utils;

/// <summary>
/// 音频数据包，用于异步处理
/// </summary>
public class AudioDataPacket
{
    public byte[] OpusData { get; set; }
    public DateTime ReceivedTime { get; set; }
    public int SequenceNumber { get; set; }
}

/// <summary>
/// 异步音频解码处理器
/// </summary>
public class AsyncAudioDecoder
{
    private readonly ConcurrentQueue<AudioDataPacket> _pendingPackets = new();
    private readonly IAudioManagerSystem _audioManager;
    private readonly CancellationTokenSource _cancellationTokenSource = new();
    private Task _processingTask;
    private volatile bool _isRunning;

    // 性能统计
    private int _processedPackets;
    private int _droppedPackets;
    private float _averageProcessingTime;

    public AsyncAudioDecoder(IAudioManagerSystem audioManager)
    {
        _audioManager = audioManager ?? throw new ArgumentNullException(nameof(audioManager));
    }

    public void Start()
    {
        if (_isRunning) return;

        _isRunning = true;
        _processingTask = Task.Run(ProcessAudioPacketsAsync, _cancellationTokenSource.Token);
        Debug.Log("异步音频解码器已启动");
    }

    public void Stop()
    {
        if (!_isRunning) return;

        _isRunning = false;
        _cancellationTokenSource.Cancel();

        try
        {
            _processingTask?.Wait(1000); // 等待最多1秒
        }
        catch (Exception ex)
        {
            Debug.LogError($"停止音频解码器时出错: {ex.Message}");
        }

        Debug.Log($"异步音频解码器已停止。处理包数: {_processedPackets}, 丢弃包数: {_droppedPackets}");
    }

    public void EnqueueAudioData(byte[] opusData)
    {
        if (!_isRunning || opusData == null || opusData.Length == 0)
            return;

        var packet = new AudioDataPacket
        {
            OpusData = new byte[opusData.Length],
            ReceivedTime = DateTime.UtcNow,
            SequenceNumber = _processedPackets + _droppedPackets
        };

        Array.Copy(opusData, packet.OpusData, opusData.Length);

        // 限制队列大小，防止内存溢出
        const int maxQueueSize = 50; // 最多缓存50个包
        if (_pendingPackets.Count >= maxQueueSize)
        {
            // 丢弃最旧的包
            if (_pendingPackets.TryDequeue(out _))
            {
                _droppedPackets++;
                Debug.LogWarning("音频解码队列已满，丢弃旧数据包");
            }
        }

        _pendingPackets.Enqueue(packet);
    }

    private async Task ProcessAudioPacketsAsync()
    {
        Debug.Log("音频解码处理线程已启动");

        while (_isRunning && !_cancellationTokenSource.Token.IsCancellationRequested)
        {
            try
            {
                if (_pendingPackets.TryDequeue(out AudioDataPacket packet))
                {
                    var startTime = DateTime.UtcNow;

                    // 解码音频数据
                    var pcmData = _audioManager.DecodeAudio(packet.OpusData);

                    if (pcmData != null && pcmData.Length > 0)
                    {
                        // 在主线程中播放音频
                        UnityMainThreadDispatcher.Instance.Enqueue(() =>
                        {
                            _audioManager.PlayAudio(pcmData);
                        });

                        _processedPackets++;

                        // 更新性能统计
                        var processingTime = (float)(DateTime.UtcNow - startTime).TotalMilliseconds;
                        _averageProcessingTime = (_averageProcessingTime * (_processedPackets - 1) + processingTime) / _processedPackets;
                    }
                    else
                    {
                        Debug.LogError("音频解码失败");
                        _droppedPackets++;
                    }
                }
                else
                {
                    // 没有数据时短暂休眠
                    await Task.Delay(1, _cancellationTokenSource.Token);
                }
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                Debug.LogError($"音频解码处理异常: {ex.Message} {ex.StackTrace}");
                _droppedPackets++;
            }
        }

        Debug.Log("音频解码处理线程已退出");
    }

    public string GetStatistics()
    {
        return $"已处理: {_processedPackets}, 已丢弃: {_droppedPackets}, " +
               $"队列长度: {_pendingPackets.Count}, 平均处理时间: {_averageProcessingTime:F2}ms";
    }
}

public interface IWebSocketSystem : ISystem {
    void Connect(string url);
    void Check(string url, Action onSuccess = null, Action onError = null);
    void Disconnect();
    void SendMessage(string message);
    void SendBinary(byte[] data);
    public WebSocketStates State { get; }
    string GetWebSocketUrl();
    event Action OnOpen;
    event Action<string> OnMessage;
    event Action OnClose;
}

public class WebSocketSystem : AbstractSystem, IWebSocketSystem {
    private IAudioManagerSystem _audioManager;
    private AsyncAudioDecoder _audioDecoder;
    private WebSocket ws;
    private bool isOpen;
    public string WsUrl => "ws://172.16.14.15:8000/";
    public event Action OnOpen;
    public event Action<string> OnMessage;
    public event Action OnClose;
    public WebSocketStates State { get; private set; }

    public void Connect(string url) {
        if (ws is { State: WebSocketStates.Open })
            ws.Close();

        ws = new WebSocket(new Uri(url));

        ws.OnOpen += OnWebSocketOpen;
        ws.OnClosed += OnWebSocketClosed;
        ws.OnBinary += OnBinaryMessageReceived;
        ws.OnMessage += OnMessageReceived;
        ws.OnInternalRequestCreated += OnInternalRequestCreated;

        ws.Open();
    }

    public void Disconnect() {
        if (ws is { State: not WebSocketStates.Closing and WebSocketStates.Closed }) return;
        ws.OnOpen -= OnWebSocketOpen;
        ws.OnClosed -= OnWebSocketClosed;
        ws.OnBinary -= OnBinaryMessageReceived;
        ws.OnMessage -= OnMessageReceived;
        ws.OnInternalRequestCreated -= OnInternalRequestCreated;
        ws.Close();
        ws = null;
        Debug.Log("WebSocketSystem::Disconnect()");
    }

    public void SendMessage(string message) {
        if (ws is { State: WebSocketStates.Open }) ws.Send(message);
    }

    public void SendBinary(byte[] data) {
        if (ws is { State: WebSocketStates.Open })
            ws.Send(data);
        // Debug.LogWarning($"发送数据:Binary");
    }

    private void OnInternalRequestCreated(WebSocket webSocket, HTTPRequest httpRequest) {
        Debug.Log("OnInternalRequestCreated");
        // TODO: accessToken deviceMac deviceUuid
        var accessToken = "test-token";
        var userModel = this.GetModel<IUserModel>();
        var deviceMac = userModel.Uid.ToString() + userModel.UserName;
        var deviceUuid = userModel.Uid.ToString() + userModel.UserName;
        httpRequest.SetHeader("Authorization", $"Bearer {accessToken}");
        httpRequest.SetHeader("Protocol-Version", "1");
        httpRequest.SetHeader("Device-Id", deviceMac);
        httpRequest.SetHeader("Client-Id", deviceUuid);
    }

    private void OnWebSocketOpen(WebSocket webSocket) {
        Debug.Log("WebSocket is now Open!");
        OnOpen?.Invoke();
    }

    private void OnWebSocketClosed(WebSocket webSocket, WebSocketStatusCodes code, string message) {
        Debug.Log("WebSocket is now Closed! code: " + code);
        Debug.Log("WebSocket is now Closed! message: " + message);
        OnClose?.Invoke();
        if (code == WebSocketStatusCodes.NormalClosure) {
            // Closed by request
        }
        // Error
    }

    private void OnMessageReceived(WebSocket webSocket, string message) {
        OnMessage?.Invoke(message);
    }

    private void OnBinaryMessageReceived(WebSocket webSocket, BufferSegment buffer) {
        try {
            // Debug.Log($"接收到消息 数据长度: {buffer.Data.Length}");
            var opusData = new byte[buffer.Count];
            Array.Copy(buffer.Data, opusData, buffer.Count);

            // 使用异步解码器处理音频数据，避免阻塞WebSocket接收线程
            if (_audioDecoder != null) {
                _audioDecoder.EnqueueAudioData(opusData);
            } else {
                // 回退到同步处理（兼容性）
                var pcmData = _audioManager.DecodeAudio(opusData);
                if (pcmData != null && pcmData.Length > 0) {
                    _audioManager.PlayAudio(pcmData);
                } else {
                    Debug.LogError("解码后的 PCM 数据为空或无效！");
                }
            }
        } catch (Exception ex) {
            Debug.LogError($"WebSocket receive error: {ex.Message}");
        }
    }

    public string GetWebSocketUrl() {
        return this.GetSystem<INetworkSystem>().ServerType switch {
            ServerTypeAot.Dev => "ws://172.16.14.15:8000/",
            ServerTypeAot.Test => "wss://atis-test.zjbci.com/",
            ServerTypeAot.Release => "wss://atis.zjbci.com/",
            _ => "ws://172.16.14.15:8000/"
        };
    }

    public void Check(string url, Action onSuccess = null, Action onError = null) {
        isOpen = false;
        ws = new WebSocket(new Uri(url));
        ws.SendPings = true;
        ws.PingFrequency = TimeSpan.FromMilliseconds(500);
        ws.CloseAfterNoMessage = TimeSpan.FromSeconds(1);
        ws.OnClosed += (_, code, message) => {
            if (isOpen) return;
            Debug.LogWarning($"❌ 连接失败或服务器未开启：{code} - {message}");
            onError?.Invoke();
        };
        ws.OnOpen += (_) => {
            Debug.LogWarning("WebSocket 连接已打开");
            isOpen = true;
            ws.Close();
            Debug.LogWarning("✅ 连接成功");
            onSuccess?.Invoke();
        };
        ws.Open();
    }

    protected override void OnInit() {
        _audioManager = this.GetSystem<IAudioManagerSystem>();

        // 初始化异步音频解码器
        _audioDecoder = new AsyncAudioDecoder(_audioManager);
        _audioDecoder.Start();

        Debug.Log("WebSocket系统已初始化，异步音频解码器已启动");
    }

    protected override void OnDeinit() {
        // 停止异步解码器
        _audioDecoder?.Stop();
        _audioDecoder = null;

        Debug.Log("WebSocket系统已清理");
    }
}
