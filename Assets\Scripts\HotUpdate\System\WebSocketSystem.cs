using System;
using Best.HTTP;
using Best.HTTP.Shared.PlatformSupport.Memory;
using Best.WebSockets;
using Best.WebSockets.Implementations;
using QFramework;
using UnityEngine;
using Builtin.Utility;
using Utils;

public interface IWebSocketSystem : ISystem {
    void Connect(string url);
    void Check(string url, Action onSuccess = null, Action onError = null);
    void Disconnect();
    void SendMessage(string message);
    void SendBinary(byte[] data);
    public WebSocketStates State { get; }
    string GetWebSocketUrl();
    event Action OnOpen;
    event Action<string> OnMessage;
    event Action OnClose;
}

public class WebSocketSystem : AbstractSystem, IWebSocketSystem {
    private IAudioManagerSystem _audioManager;
    private WebSocket ws;
    private bool isOpen;
    public string WsUrl => "ws://172.16.14.15:8000/";
    public event Action OnOpen;
    public event Action<string> OnMessage;
    public event Action OnClose;
    public WebSocketStates State { get; private set; }

    public void Connect(string url) {
        if (ws is { State: WebSocketStates.Open })
            ws.Close();

        ws = new WebSocket(new Uri(url));

        ws.OnOpen += OnWebSocketOpen;
        ws.OnClosed += OnWebSocketClosed;
        ws.OnBinary += OnBinaryMessageReceived;
        ws.OnMessage += OnMessageReceived;
        ws.OnInternalRequestCreated += OnInternalRequestCreated;

        ws.Open();
    }

    public void Disconnect() {
        if (ws is { State: not WebSocketStates.Closing and WebSocketStates.Closed }) return;
        ws.OnOpen -= OnWebSocketOpen;
        ws.OnClosed -= OnWebSocketClosed;
        ws.OnBinary -= OnBinaryMessageReceived;
        ws.OnMessage -= OnMessageReceived;
        ws.OnInternalRequestCreated -= OnInternalRequestCreated;
        ws.Close();
        ws = null;
        Debug.Log("WebSocketSystem::Disconnect()");
    }

    public void SendMessage(string message) {
        if (ws is { State: WebSocketStates.Open }) ws.Send(message);
    }

    public void SendBinary(byte[] data) {
        if (ws is { State: WebSocketStates.Open })
            ws.Send(data);
        // Debug.LogWarning($"发送数据:Binary");
    }

    private void OnInternalRequestCreated(WebSocket webSocket, HTTPRequest httpRequest) {
        Debug.Log("OnInternalRequestCreated");
        // TODO: accessToken deviceMac deviceUuid
        var accessToken = "test-token";
        var userModel = this.GetModel<IUserModel>();
        var deviceMac = userModel.Uid.ToString() + userModel.UserName;
        var deviceUuid = userModel.Uid.ToString() + userModel.UserName;
        httpRequest.SetHeader("Authorization", $"Bearer {accessToken}");
        httpRequest.SetHeader("Protocol-Version", "1");
        httpRequest.SetHeader("Device-Id", deviceMac);
        httpRequest.SetHeader("Client-Id", deviceUuid);
    }

    private void OnWebSocketOpen(WebSocket webSocket) {
        Debug.Log("WebSocket is now Open!");
        OnOpen?.Invoke();
    }

    private void OnWebSocketClosed(WebSocket webSocket, WebSocketStatusCodes code, string message) {
        Debug.Log("WebSocket is now Closed! code: " + code);
        Debug.Log("WebSocket is now Closed! message: " + message);
        OnClose?.Invoke();
        if (code == WebSocketStatusCodes.NormalClosure) {
            // Closed by request
        }
        // Error
    }

    private void OnMessageReceived(WebSocket webSocket, string message) {
        OnMessage?.Invoke(message);
    }

    private void OnBinaryMessageReceived(WebSocket webSocket, BufferSegment buffer) {
        try {
            // Debug.Log($"接收到消息 数据长度: {buffer.Data.Length}");
            var opusData = new byte[buffer.Count];
            Array.Copy(buffer.Data, opusData, buffer.Count);

            // 这里对接收到的 Opus 数据进行解码，转换为 PCM 数据
            var pcmData = _audioManager.DecodeAudio(opusData);

            if (pcmData != null && pcmData.Length > 0) {
                // Debug.Log($"解码成功，数据长度：{pcmData.Length}");
                // 确保无论是否有麦克风，都尝试播放音频
                _audioManager.PlayAudio(pcmData);
                // 添加日志以确认播放尝试
                //Debug.Log("尝试播放音频数据");
            }
            else
                Debug.LogError("解码后的 PCM 数据为空或无效！");
        } catch (Exception ex) {
            Debug.LogError($"WebSocket receive error: {ex.Message}");
        }
    }

    public string GetWebSocketUrl() {
        return this.GetSystem<INetworkSystem>().ServerType switch {
            ServerTypeAot.Dev => "ws://172.16.14.15:8000/",
            ServerTypeAot.Test => "wss://atis-test.zjbci.com/",
            ServerTypeAot.Release => "wss://atis.zjbci.com/",
            _ => "ws://172.16.14.15:8000/"
        };
    }

    public void Check(string url, Action onSuccess = null, Action onError = null) {
        isOpen = false;
        ws = new WebSocket(new Uri(url));
        ws.SendPings = true;
        ws.PingFrequency = TimeSpan.FromMilliseconds(500);
        ws.CloseAfterNoMessage = TimeSpan.FromSeconds(1);
        ws.OnClosed += (_, code, message) => {
            if (isOpen) return;
            Debug.LogWarning($"❌ 连接失败或服务器未开启：{code} - {message}");
            onError?.Invoke();
        };
        ws.OnOpen += (_) => {
            Debug.LogWarning("WebSocket 连接已打开");
            isOpen = true;
            ws.Close();
            Debug.LogWarning("✅ 连接成功");
            onSuccess?.Invoke();
        };
        ws.Open();
    }

    protected override void OnInit() {
        _audioManager = this.GetSystem<IAudioManagerSystem>();
    }
}
