﻿/// <summary>
///     树形菜单数据
/// </summary>
public class TreeViewData {
    /// <summary>
    ///     本节点ID
    /// </summary>
    public int ID;

    /// <summary>
    ///     是否是文件
    /// </summary>
    public bool IsFile = false;

    /// <summary>
    ///     数据内容
    /// </summary>
    public string Name;

    /// <summary>
    ///     数据所属的父ID
    /// </summary>
    public int ParentID;

    /// <summary>
    ///     数据内容
    /// </summary>
    public string Path;
}
