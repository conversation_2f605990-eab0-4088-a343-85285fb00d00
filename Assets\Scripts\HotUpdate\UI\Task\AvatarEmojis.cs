using QFramework;
using UnityEngine;
using UnityEngine.ResourceManagement.AsyncOperations;
using UnityEngine.UI;

public class AvatarEmojis : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IController {
    [SerializeField] public Image image;

    private const string EmojisDir = "Assets/AA/Sprites/emojis/";
    private string _last_emoji = "";
    private Sprite _defaultEmojiSprite;

    private async void Start() {
        var obj = await this.GetSystem<IAddressableSystem>().LoadAssetAsync<Sprite>(GetPicPath("neutral"));
        if (obj.Status == AsyncOperationStatus.Succeeded) {
            _defaultEmojiSprite = obj.Result;
        }
        this.RegisterEvent<AvatarEmojiEvent>(PlayEmoji).UnRegisterWhenGameObjectDestroyed(gameObject);
    }


    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    private async void PlayEmoji(AvatarEmojiEvent e) {
        if (e.mEmoji == _last_emoji) return;
        _last_emoji = e.mEmoji;
        var obj = await this.GetSystem<IAddressableSystem>().LoadAssetAsync<Sprite>(GetPicPath(e.mEmoji));
        if (obj.Status == AsyncOperationStatus.Succeeded) {
            image.sprite = obj.Result;
        } else {
            if (_defaultEmojiSprite != null) {
                image.sprite = _defaultEmojiSprite;
            }
        }
    }

    private static string GetPicPath(string emoji) {
        return EmojisDir + emoji + ".gif";
    }
}
