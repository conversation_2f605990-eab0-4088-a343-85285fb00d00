﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Builtin.Utility;
using Cysharp.Threading.Tasks;
using QFramework;
using UnityEngine;
using UnityEngine.Networking;

/// <summary>
///     定义网络系统的接口，继承自ISystem接口
/// </summary>
public interface INetworkSystem : ISystem {
    /// <summary>
    ///     获取或设置服务器类型
    /// </summary>
    public ServerTypeAot ServerType { get; set; }

    /// <summary>
    ///     获取或设置HTTP请求的基础URL
    /// </summary>
    public string HttpBaseUrl { get; set; }

    /// <summary>
    ///     发送HTTP GET请求
    /// </summary>
    /// <typeparam name="T">响应数据的类型</typeparam>
    /// <param name="endpoint">请求的端点</param>
    /// <param name="token">可选的认证令牌</param>
    /// <returns>异步返回HTTP请求结果</returns>
    public UniTask<HttpResult<T>> Get<T>(string endpoint, string token = null);

    /// <summary>
    ///     发送HTTP POST请求，请求体为JSON格式
    /// </summary>
    /// <typeparam name="T">响应数据的类型</typeparam>
    /// <param name="endpoint">请求的端点</param>
    /// <param name="payload">请求体数据</param>
    /// <param name="token">可选的认证令牌</param>
    /// <returns>异步返回HTTP请求结果</returns>
    public UniTask<HttpResult<T>> PostJson<T>(string endpoint, object payload = null, string token = null);

    /// <summary>
    ///     发送HTTP POST请求，请求体为表单数据
    /// </summary>
    /// <typeparam name="T">响应数据的类型</typeparam>
    /// <param name="endpoint">请求的端点</param>
    /// <param name="formData">表单数据</param>
    /// <param name="token">可选的认证令牌</param>
    /// <returns>异步返回HTTP请求结果</returns>
    public UniTask<HttpResult<T>> PostForm<T>(string endpoint, MultipartFormData formData, string token = null);

    public UniTask<UploadResult> UploadFileAsync(
        string filePath,
        string endpoint,
        Dictionary<string, string> formFields = null,
        string mimeType = "application/octet-stream",
        string token = null);

    public UniTask<UserInfo> GetUserInfo(int uid);
}

/// <summary>
///     网络系统的实现类，继承自AbstractSystem并实现INetworkSystem接口
/// </summary>
public class NetworkSystem : AbstractSystem, INetworkSystem {
    // 私有字段，存储请求超时时间
    private readonly float _timeout = 30f;

    // 私有字段，存储服务器类型

    /// <summary>
    ///     获取或设置服务器类型
    /// </summary>
    public ServerTypeAot ServerType { get; set; }

    /// <summary>
    ///     获取或设置HTTP请求的基础URL
    /// </summary>
    public string HttpBaseUrl { get; set; }

    public async UniTask<UserInfo> GetUserInfo(int uid) {
        try {
            // 构建请求数据对象
            var requestData = new UserInfoRequest { uid = uid };

            // 发送POST请求
            var result = await PostJson<UserInfo>(
                RequestUrl.getUserInfo,
                requestData,
                this.GetModel<IUserModel>().Token.Value
            );

            // 处理响应
            if (result.IsSuccess) return this.GetSystem<IDataParseSystem>().ParseUserInfo(result.Data);

            Debug.LogError($"获取用户信息失败 [{result.StatusCode}]: {result.ErrorMessage}");
            return null; // 或抛出异常
        } catch (Exception ex) {
            Debug.LogException(ex);
            return null;
        }
    }

    #region 核心请求方法

    private async UniTask<HttpResult<T>> SendRequest<T>(
        string endpoint,
        string method,
        byte[] body = null,
        string contentType = HttpContentType.Json,
        string token = null
    ) {
        if (Application.internetReachability == NetworkReachability.NotReachable) {
            Debug.LogError("断网了...");

            var info = new InfoConfirmInfo("",
                "我找不到网络信号了...\n可能是WiFi偷懒啦！\n请检查WiFi\n点击确定后，将退出APP",
                Application.Quit, type: ConfirmAlertType.Single);
            this.SendEvent(new ShowPageEvent(UIPageType.InfoConfirmAlert, UILevelType.Alart, info));
            return new HttpResult<T>(-1, "Unexpected error");
        }

        var url = CombineUrl(HttpBaseUrl, endpoint);
        using var request = new UnityWebRequest(url, method) {
            timeout = (int)_timeout,
            downloadHandler = new DownloadHandlerBuffer()
        };

        ConfigureRequest(request, body, contentType, token);
        AddDefaultHeaders(request);

        try {
            await request.SendWebRequest()
                .WithCancellation(CoroutineController.manager.GetCancellationTokenOnDestroy());
            return ProcessResponse<T>(request);
        } catch (OperationCanceledException) {
            return new HttpResult<T>(-1, "Request cancelled");
        } catch (UnityWebRequestException webEx) {
            Debug.LogError("UnityWebRequestException");
            if (request.responseCode == 401) {
                var info = new InfoConfirmInfo("", "账号已从其他设备登录\n请重新登录！\n点击确定后，将退出APP。",
                    Application.Quit
                    , type: ConfirmAlertType.Single);
                this.SendEvent(new ShowPageEvent(UIPageType.InfoConfirmAlert, UILevelType.Alart, info));
            } else if (request.responseCode == 204) {
                var info = new InfoConfirmInfo("", "Token 非法！\n请重新登录！\n点击确定后，将退出APP。",
                    Application.Quit
                    , type: ConfirmAlertType.Single);
                this.SendEvent(new ShowPageEvent(UIPageType.InfoConfirmAlert, UILevelType.Alart, info));
            } else if (request.responseCode == 203) {
                var info = new InfoConfirmInfo("", "无Token！\n请重新登录！\n点击确定后，将退出APP。",
                    Application.Quit
                    , type: ConfirmAlertType.Single);
                this.SendEvent(new ShowPageEvent(UIPageType.InfoConfirmAlert, UILevelType.Alart, info));
            }

            return HandleWebError<T>(webEx);
        } catch (Exception ex) {
            Debug.LogError($"Critical error: {ex}");
            return new HttpResult<T>(-1, "Unexpected error");
        }
    }

    #endregion

    /// <summary>
    ///     系统初始化方法
    /// </summary>
    protected override void OnInit() {
    }

    #region 公开接口

    public UniTask<HttpResult<T>> Get<T>(string endpoint, string token = null) {
        return SendRequest<T>(endpoint, UnityWebRequest.kHttpVerbGET, token: token);
    }

    public UniTask<HttpResult<T>> PostJson<T>(string endpoint, object payload, string token = null) {
        var json = JsonUtility.ToJson(payload);
        var data = Encoding.UTF8.GetBytes(json);
        return SendRequest<T>(endpoint, UnityWebRequest.kHttpVerbPOST, data,
            HttpContentType.Json, token);
    }

    public UniTask<HttpResult<T>> PostForm<T>(
        string endpoint,
        MultipartFormData formData,
        string token = null
    ) {
        return SendRequest<T>(endpoint, UnityWebRequest.kHttpVerbPOST, formData.GetBytes(),
            formData.ContentType, token);
    }

    #endregion

    #region 私有辅助方法

    private void ConfigureRequest(UnityWebRequest request, byte[] body, string contentType, string token) {
        if (body != null && body.Length > 0)
            request.uploadHandler = new UploadHandlerRaw(body) {
                contentType = contentType
            };

        if (!string.IsNullOrEmpty(token)) request.SetRequestHeader("Authorization", $"{token}");
    }

    private void AddDefaultHeaders(UnityWebRequest request) {
        request.SetRequestHeader("Accept", HttpContentType.Json);
        request.SetRequestHeader("X-Client-Version", Application.version);
    }

    private HttpResult<T> ProcessResponse<T>(UnityWebRequest request) {
        if (request.result != UnityWebRequest.Result.Success) {
            Debug.LogError($"HTTP Error: {request.error} | {request.downloadHandler?.text} | {request.responseCode}");
            return new HttpResult<T>(
                (int)request.responseCode,
                $"{request.error} | {request.downloadHandler?.text}"
            );
        }

        try {
            var json = request.downloadHandler.text;
            var data = JsonUtility.FromJson<BaseResponse<T>>(json);

            return new HttpResult<T>(data);
        } catch (Exception ex) {
            Debug.LogError($"JSON Parse Error: {ex.Message}");
            return new HttpResult<T>(
                (int)request.responseCode,
                $"{request.error} | {request.downloadHandler?.text}"
            );
        }
    }

    private HttpResult<T> HandleWebError<T>(UnityWebRequestException ex) {
        var errorMessage = ex.Message;
        if (ex.Result == UnityWebRequest.Result.ProtocolError) {
            errorMessage += $"\nStatus: {ex.ResponseCode}";
            errorMessage += $"\nResponse: {ex.Text}";
        }

        return new HttpResult<T>((int)ex.ResponseCode, errorMessage);
    }

    private static string CombineUrl(string baseUrl, string endpoint) {
        if (string.IsNullOrEmpty(endpoint)) return baseUrl;
        return $"{baseUrl.TrimEnd('/')}/{endpoint.TrimStart('/')}";
    }

    #endregion


    #region 文件上传

    // 文件上传方法
    public async UniTask<UploadResult> UploadFileAsync(
        string filePath,
        string endpoint,
        Dictionary<string, string> formFields = null,
        string mimeType = "application/octet-stream",
        string token = null) {
        try {
            // 1. 读取文件内容
            var fileBytes = await ReadFileAsync(filePath);
            if (fileBytes == null || fileBytes.Length == 0)
                return new UploadResult {
                    Success = false,
                    ErrorMessage = "文件内容为空",
                    StatusCode = -1
                };

            // 2. 构建多部分表单数据
            var formData = new MultipartFormData();

            // 添加文件
            var fileName = Path.GetFileName(filePath);
            formData.AddFile(
                "file",
                fileBytes,
                fileName,
                mimeType);

            // 添加额外字段
            if (formFields != null)
                foreach (var field in formFields)
                    formData.AddField(field.Key, field.Value);


            // 3. 发送请求
            var result = await PostForm<object>(
                endpoint,
                formData,
                token);

            // 4. 处理响应
            if (result.IsSuccess)
                return new UploadResult {
                    Success = true,
                    StatusCode = result.StatusCode
                };

            return new UploadResult {
                Success = false,
                ErrorMessage = $"上传失败 [{result.StatusCode}]: {result.ErrorMessage}",
                StatusCode = result.StatusCode
            };
        } catch (Exception ex) {
            Debug.LogError($"上传异常: {ex}");
            return new UploadResult {
                Success = false,
                ErrorMessage = $"客户端异常: {ex.Message}",
                StatusCode = -1
            };
        }
    }

    /// <summary>
    ///     异步读取文件内容
    /// </summary>
    /// <param name="path">文件路径</param>
    /// <returns>异步返回文件内容的字节数组</returns>
    private async UniTask<byte[]> ReadFileAsync(string path) {
        // 使用文件流打开文件
        using var fs = new FileStream(path, FileMode.Open);
        // 创建字节数组，用于存储文件内容
        var buffer = new byte[fs.Length];
        // 异步读取文件内容到字节数组中
        await fs.ReadAsync(buffer, 0, (int)fs.Length);
        // 返回文件内容的字节数组
        return buffer;
    }

    /// <summary>
    ///     上传响应的序列化类
    /// </summary>
    [Serializable]
    private class UploadResponse {
        // 服务器上的文件路径
        public string ServerPath;

        // 文件上传时间
        public DateTime UploadTime;
    }

    #endregion
}

[Serializable]
public class BaseResponse<T> {
    public int code;
    public string msg;
    public T data;
}

/// <summary>
///     响应结果的封装类
/// </summary>
/// <typeparam name="T">响应数据的类型</typeparam>
public class HttpResult<T> {
    /// <summary>
    ///     构造函数，用于创建成功的响应结果
    /// </summary>
    /// <param name="response">响应数据</param>
    public HttpResult(BaseResponse<T> response) {
        IsSuccess = response.code == 200;
        StatusCode = response.code;
        Data = response.data;
        ErrorMessage = response.msg;
    }

    /// <summary>
    ///     构造函数，用于创建失败的响应结果
    /// </summary>
    /// <param name="statusCode">响应状态码</param>
    /// <param name="error">错误信息</param>
    /// <param name="rawData">原始响应数据，默认为null</param>
    public HttpResult(int statusCode, string error) {
        IsSuccess = false;
        StatusCode = statusCode;
        ErrorMessage = error;
    }

    /// <summary>
    ///     获取请求是否成功的标志
    /// </summary>
    public bool IsSuccess { get; }

    /// <summary>
    ///     获取响应数据
    /// </summary>
    public T Data { get; }

    /// <summary>
    ///     获取响应状态码
    /// </summary>
    public long StatusCode { get; }

    /// <summary>
    ///     获取错误信息
    /// </summary>
    public string ErrorMessage { get; }

    /// <summary>
    ///     获取原始响应数据
    /// </summary>
    public byte[] RawData { get; }
}

/// <summary>
///     多部分表单数据构造器
/// </summary>
public class MultipartFormData {
    // 存储表单边界的字节数组
    private readonly byte[] _boundary;

    // 存储表单部分的列表
    private readonly List<IMultipartFormSection> _formSections = new();

    /// <summary>
    ///     构造函数，生成随机边界值
    /// </summary>
    public MultipartFormData() {
        // 生成随机边界值
        _boundary = UnityWebRequest.GenerateBoundary();
    }

    /// <summary>
    ///     获取表单数据的内容类型
    /// </summary>
    public string ContentType => $"multipart/form-data; boundary={Encoding.UTF8.GetString(_boundary)}";

    /// <summary>
    ///     添加表单字段
    /// </summary>
    /// <param name="name">字段名</param>
    /// <param name="value">字段值</param>
    public void AddField(string name, string value) {
        // 将字段添加到表单部分列表中
        _formSections.Add(new MultipartFormDataSection(name, value));
    }

    /// <summary>
    ///     添加文件到表单
    /// </summary>
    /// <param name="name">字段名</param>
    /// <param name="data">文件内容的字节数组</param>
    /// <param name="fileName">文件名</param>
    /// <param name="mimeType">文件的MIME类型</param>
    public void AddFile(string name, byte[] data, string fileName, string mimeType) {
        // 将文件添加到表单部分列表中
        _formSections.Add(new MultipartFormFileSection(name, data, fileName, mimeType));
    }

    /// <summary>
    ///     获取表单数据的字节数组
    /// </summary>
    /// <returns>表单数据的字节数组</returns>
    public byte[] GetBytes() {
        // 序列化表单部分为字节数组
        return UnityWebRequest.SerializeFormSections(
            _formSections,
            _boundary
        );
    }
}

/// <summary>
///     定义HTTP内容类型的常量类
/// </summary>
public static class HttpContentType {
    /// <summary>
    ///     JSON内容类型
    /// </summary>
    public const string Json = "application/json";

    /// <summary>
    ///     表单数据内容类型
    /// </summary>
    public const string FormData = "multipart/form-data";

    /// <summary>
    ///     二进制流内容类型
    /// </summary>
    public const string OctetStream = "application/octet-stream";
}

// 响应数据结构
[Serializable]
public class UploadResponse {
    public string FilePath;
    public long FileSize;
    public DateTime UploadTime;
}

// 上传结果封装
public struct UploadResult {
    public bool Success;
    public string ServerPath;
    public long FileSize;
    public long StatusCode;
    public string ErrorMessage;
}
