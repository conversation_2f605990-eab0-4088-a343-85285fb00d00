﻿using System.Collections.Generic;
using System.Linq;
using QFramework;
using UnityEngine;

public interface ILogModel : IModel {
    public List<string> UnUploadLogList { get; set; }
}

public class LogModel : AbstractModel, ILogModel {
    public List<string> UnUploadLogList { get; set; } = new();

    protected override void OnInit() {
        UnUploadLogList = PlayerPrefs.GetString(PrefKeys.UnUploadLogList).Split('*').ToList();
        Debug.Log("LogModel OnInit: UnUploadLogList:" + string.Join("*", UnUploadLogList));
    }
}
