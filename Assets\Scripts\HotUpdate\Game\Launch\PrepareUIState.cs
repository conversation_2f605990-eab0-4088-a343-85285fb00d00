using QFramework;
using UnityEngine;

public class PrepareUIState : AbstractState<LaunchStates, Launch>, IController {
    public PrepareUIState(FSM<LaunchStates> fsm, Launch target) : base(fsm, target) {
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    protected override void OnEnter() {
        Debug.Log("PrepareUIState OnEnter");
        this.SendCommand(new PrepareUICommand());
        ChangeState();
    }

    private void ChangeState() {
        mFSM.ChangeState(LaunchStates.AssetsUpdate);
    }

    protected override void OnExit() {
        // loading 的特殊性，可能还未准备好，就会被关闭，所以提前准备
        this.SendCommand(new ShowPageCommand(UIPageType.LoadingUI, UILevelType.Prepare));
    }
}
