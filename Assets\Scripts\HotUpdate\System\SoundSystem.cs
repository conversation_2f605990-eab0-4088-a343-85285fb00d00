﻿using System;
using QFramework;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.ResourceManagement.AsyncOperations;

public enum SoundType {
    Button_Low,
    Close,
    BackgroundMusic,
    Win
}

public interface ISoundSystem : ISystem {
    public void PlayBackgroundMusic();
    public void PlaySound(SoundType type);
    public void StopAllSound();
    public void SetSound(bool isOn);
    public void SetBackgroundMusic(bool isOn);
}

public class SoundSystem : AbstractSystem, ISoundSystem {
    private readonly string path = "Assets/AA/Audio/Sounds/";

    public void PlaySound(SoundType soundType) {
        AudioKit.PlaySound(path + soundType + ".wav");
    }

    public void PlayBackgroundMusic() {
        AudioKit.PlayMusic(path + SoundType.BackgroundMusic + ".wav");
    }

    public void StopAllSound() {
        AudioKit.StopAllSound();
    }

    public void SetBackgroundMusic(bool isOn) {
        AudioKit.Settings.IsMusicOn.Value = isOn;
        if (isOn) PlayBackgroundMusic();
    }

    public void SetSound(bool isOn) {
        AudioKit.Settings.IsSoundOn.Value = isOn;
    }

    protected override void OnInit() {
        AudioKit.Config.AudioLoaderPool = new AddressableAudioLoaderPool();


        this.RegisterEvent<ChangeSettingEvent>(e => {
            SetSound(this.GetModel<ISettingsModel>().IsOnSound.Value);
            SetBackgroundMusic(this.GetModel<ISettingsModel>().IsOnMusic.Value);
        });
    }

    /// <summary>
    ///     定义从 Addressable 加载音频
    /// </summary>
    private class AddressableAudioLoaderPool : AbstractAudioLoaderPool {
        protected override IAudioLoader CreateLoader() {
            return new AddressableAudioLoader();
        }
    }

    private class AddressableAudioLoader : IAudioLoader {
        private AsyncOperationHandle<AudioClip> mHandle;

        public AudioClip Clip { get; private set; }

        public AudioClip LoadClip(AudioSearchKeys audioSearchKeys) {
            // 同步加载（注意：可能造成主线程卡顿）
            mHandle = Addressables.LoadAssetAsync<AudioClip>(audioSearchKeys.AssetName);
            Clip = mHandle.WaitForCompletion();
            return Clip;
        }

        public void LoadClipAsync(AudioSearchKeys audioSearchKeys, Action<bool, AudioClip> onLoad) {
            mHandle = Addressables.LoadAssetAsync<AudioClip>(audioSearchKeys.AssetName);
            mHandle.Completed += handle => {
                if (handle.Status == AsyncOperationStatus.Succeeded) {
                    Clip = handle.Result;
                    onLoad(true, Clip);
                } else {
                    onLoad(false, null);
                }
            };
        }

        public void Unload() {
            if (mHandle.IsValid()) {
                // 释放资源
                Addressables.Release(mHandle);
                Clip = null;
            }
        }
    }
}
