﻿/// <summary>
///     请求网址
/// </summary>
public static class RequestUrl {
    public static string loginUrl = "client/Login";
    public static string recordLogin = "client/UserInfo/recordLogin";
    public static string avatarUrl = "client/Avatar/Detail";
    public static string changeAvatarUrl = "client/Avatar/Change";
    public static string buyAvatarUrl = "client/Avatar/Buy";
    public static string modifyPersonalInfoUrl = "client/UserInfo/ModifyPassword";
    public static string getUserInfo = "client/UserInfo/GetUser";
    public static string getTaskUrl = "client/GetTask";
    public static string sendTaskIsComplete = "client/TaskIsComplete";
    public static string uploadChatHistory = "client/UploadChatHistory";
    public static string getTaskScheduleUrl = "client/GetTaskScheduleUrl";
    public static string questionnaireUrl = "client/QuestionnaireUrl";
    public static string isQuestionnaireUrl = "client/IsQuestionnaire";
    public static string UpdateLogUrl = "client/UpdateLog";
    public static string UploadEEGUrl = "client/UploadEEG";

    // public static string registerUrl = "v1/Register";
    // public static string enterRoomUrl = "v2/Game/EnterGame";  // ???
}

public static class PrefKeys {
    public const string userName = "userName";
    public const string password = "password";
    public const string rememberPassword = "rememberPassword";
    public const string isCompleteGuidance = "isCompleteGuidance";
    public const string isSuperuser = "isSuperuser";
    public const string Aid = "Aid";
    public const string isActiveEEG = "isActiveEEG";
    public const string chatType = "chatType";
    public const string AudioVisualStepCount = "AudioVisualStepCount";
    public const string CharacterId = "CharacterId";
    public const string isCorrectedEEG = "isCorrectedEEG";
    public const string isCorrectedEEGRoot = "isCorrectedEEGRoot";
    public const string LastResetTime = "LastResetTime";
    public const string LastResetTicks = "LastResetTicks";
    public const string UnUploadLogList = "UnUploadLogList";
    public const string CalibrationEEGDataFilePath = "CalibrationEEGDataFilePath";
    public const string ConnectedEEGDeviceAddress = "ConnectedEEGDeviceAddress";
    public const string VoiceServiceRate = "VoiceService.Rate";
}


public static class ScenePath {
    public static string Login = "Login"; // 登录页面
    public static string Index = "Index"; // 主页面
    public static string Loading = "Loading"; // 加载页面
    public static string Task = "Task"; // 任务界面 各个任务范式穿插其中
}
