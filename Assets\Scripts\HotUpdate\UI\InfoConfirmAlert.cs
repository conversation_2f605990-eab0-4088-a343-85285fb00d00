﻿using System;
using System.Collections.Generic;
using QFramework;
using UnityEngine;
using UnityEngine.UI;
using Utils;

public enum ConfirmAlertType {
    Double = 0,
    Single
}

public class InfoConfirmInfo {
    public string cancelText;
    public string confirmText;
    public string content;
    public Action fail;
    public Action succ;
    public string title;
    public ConfirmAlertType type;

    public InfoConfirmInfo(string title = "提示", string content = "", Action success = null, Action fail = null,
        string confirmText = "确认", string cancelText = "取消", ConfirmAlertType type = ConfirmAlertType.Double) {
        this.title = title;
        this.content = content;
        succ = success;
        this.fail = fail;
        this.confirmText = confirmText;
        this.cancelText = cancelText;
        this.type = type;
    }
}

public class InfoConfirmAlert : UIPenal {
    public Text titleText;
    public Text contentText;
    public Button cancelBtn;
    public Button confirmBtn;
    public Text confirmText;
    public Text cancelText;
    public RectTransform rectTransformText;
    public RectTransform rectTransformBg;

    private readonly Queue<InfoConfirmInfo> queue = new();
    private Action fail;

    private Action success;

    private void Start() {
        cancelBtn.onClick.AddListener(() => {
            fail?.Invoke();
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            if (queue.Count == 0)
                gameObject.SetActive(false);
            else
                UpdateUI(queue.Dequeue());
        });

        confirmBtn.onClick.AddListener(() => {
            success?.Invoke();
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            if (queue.Count == 0)
                gameObject.SetActive(false);
            else
                UpdateUI(queue.Dequeue());
        });
    }

    public override void InitData(object data) {
        var info = data as InfoConfirmInfo;
        if (info == null) return;
        ShowWithText(info.title, info.content, info.succ, info.fail, info.confirmText, info.cancelText, info.type);
    }

    private void ShowWithText(string title = "提示", string content = "", Action success = null, Action fail = null,
        string confirmText = "确认", string cancelText = "取消", ConfirmAlertType type = ConfirmAlertType.Double) {
        var info = new InfoConfirmInfo(title, content, success, fail, confirmText, cancelText, type);
        queue.Enqueue(info);
        UpdateUI(queue.Dequeue());
    }

    private void UpdateUI(InfoConfirmInfo info) {
        gameObject.SetActiveFast(true);

        contentText.text = info.content;
        success = info.succ;
        fail = info.fail;
        cancelBtn.gameObject.SetActive(info.type == ConfirmAlertType.Double);
        confirmText.text = info.confirmText;
        cancelText.text = info.cancelText;
        titleText.text = info.title;

        rectTransformText.sizeDelta = new Vector2(contentText.preferredWidth, contentText.preferredHeight);
        rectTransformBg.sizeDelta = new Vector2(
            contentText.preferredWidth + 120 > 740 ? contentText.preferredWidth + 120 : 740,
            contentText.preferredHeight + 258 > 440 ? contentText.preferredHeight + 258 : 440
        );
    }

    public string GetI18NText(string showText) {
        if (this.GetSystem<II18NSystem>().InitFinish) return this.GetSystem<II18NSystem>().GetText(showText);

        return showText;
    }
}
