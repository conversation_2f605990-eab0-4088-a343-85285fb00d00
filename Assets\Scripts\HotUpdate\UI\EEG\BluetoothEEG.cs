﻿using System;
using System.Collections;
using System.IO;
using System.Text;
using System.Threading;
using Cysharp.Threading.Tasks;
using DTC;
using QFramework;
using UnityEngine;
using UnityEngine.SceneManagement;
using Utils;

public class BluetoothEEG : MonoBehaviour, IController {
    private readonly StringBuilder _dataBuffer = new();
    private CancellationTokenSource _connectionCts;

    private IEegModel _eegModel;
    private bool _isGetRawData;
    public static BluetoothEEG Instance { get; private set; }

    private void Awake() {
        if (Instance == null) {
            Instance = this;
            DontDestroyOnLoad(gameObject);

            _eegModel = this.GetModel<IEegModel>();
            InitializeBluetooth();
        } else if (Instance != this) {
            Destroy(gameObject);
        }
    }

    private void Update() {
        if (!_eegModel.isActive.Value) return;
        if (Application.platform != RuntimePlatform.Android) return;

        UpdateConnectionStatus();
    }

    private void OnDestroy() {
        if (Instance == this) Cleanup();
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    private void InitializeBluetooth() {
        if (Application.platform != RuntimePlatform.Android) return;

        _eegModel.bluetoothManager.CheckPermission();
        _eegModel.bluetoothManager.OnRefreshFoundDevicesCB += OnRefreshFoundDevices;
        _eegModel.enterTechBleManager.OnGetOneSecondRawDataCB += OnEegDataReceived;

        if (_eegModel.isActive.Value) {
            _eegModel.bluetoothManager.Scan();
            this.SendCommand<ConnectBleEegCommand>();
        }

        this.RegisterEvent<ConnectBleEegEvent>(OnConnectBleRequested)
            .UnRegisterWhenGameObjectDestroyed(gameObject);
        this.RegisterEvent<GetEegBatteryEvent>(OnGetBatteryRequested)
            .UnRegisterWhenGameObjectDestroyed(gameObject);
    }

    private void UpdateConnectionStatus() {
        if (_eegModel.bluetoothManager.GetBindDevice() != null) {
            if (!string.IsNullOrEmpty(_eegModel.bluetoothManager.ConnectedDeviceAddress)) {
                _eegModel.SetEegStatus(_eegModel.enterTechBleManager.IsTouchSkin
                    ? EegStatusEnum.Connected
                    : EegStatusEnum.NoForehead);
            } else if (_eegModel.eegStatus.Value == EegStatusEnum.Connecting) {
                // Still connecting
            } else if (_eegModel.eegStatus.Value == EegStatusEnum.Scanning) {
            } else {
                _eegModel.SetEegStatus(EegStatusEnum.Disconnected);
                if (SceneManager.GetActiveScene().name == SceneID.Task.ToString() ||
                    SceneManager.GetActiveScene().name == SceneID.Index.ToString())
                    TryReconnectIfNeeded();
            }
        } else if (_eegModel.eegStatus.Value == EegStatusEnum.Connecting) {
            // Still connecting
        } else {
            _eegModel.SetEegStatus(EegStatusEnum.IsUnBindDevice);
            if (SceneManager.GetActiveScene().name == SceneID.Task.ToString()) TryReconnectIfNeeded();
        }
    }

    private void TryReconnectIfNeeded() {
        Debug.Log("脑电断开连接，自动重连。 Scene: " + SceneManager.GetActiveScene().name);
        this.SendCommand<ConnectBleEegCommand>();
    }

    private void Cleanup() {
        _connectionCts?.Cancel();
        _connectionCts?.Dispose();

        _eegModel.bluetoothManager.OnRefreshFoundDevicesCB -= OnRefreshFoundDevices;
        _eegModel.enterTechBleManager.OnGetOneSecondRawDataCB -= OnEegDataReceived;
    }

    #region Connection Management

    private async void OnConnectBleRequested(ConnectBleEegEvent e) {
        if (!_eegModel.isActive.Value) {
            _eegModel.SetEegStatus(EegStatusEnum.Inactive);
            return;
        }

        if (_eegModel.eegStatus.Value != EegStatusEnum.Disconnected &&
            _eegModel.eegStatus.Value != EegStatusEnum.Error &&
            _eegModel.eegStatus.Value != EegStatusEnum.IsUnBindDevice &&
            _eegModel.eegStatus.Value != EegStatusEnum.Scanning) return;

        _eegModel.eegStatus.Value = EegStatusEnum.Scanning;

        if (_connectionCts is { Token: { IsCancellationRequested: false } }) {
            _connectionCts.Cancel();
            _connectionCts.Dispose();
        }

        await ScanDevice();
    }

    private async UniTask ScanDevice() {
        _connectionCts = new CancellationTokenSource();

        try {
            string deviceAddress;
            if (_eegModel.bluetoothManager.GetBindDevice() != null) {
                Debug.Log("Device is already bound. Attempting to connect...");
                deviceAddress = _eegModel.bluetoothManager.GetBindDevice();
            } else {
                deviceAddress = PlayerPrefs.GetString(PrefKeys.ConnectedEEGDeviceAddress);
            }

            if (string.IsNullOrEmpty(deviceAddress)) {
                Debug.LogError("No device address available for connection");
                _eegModel.SetEegStatus(EegStatusEnum.Error);
                return;
            }

            // 等待设备发现
            if (!_eegModel.bluetoothManager.FoundDeviceDic.ContainsKey(deviceAddress))
                await UniTask.WaitUntil(
                    () => _eegModel.bluetoothManager.FoundDeviceDic.TryGetValue(deviceAddress, out var value),
                    cancellationToken: _connectionCts.Token
                ).Timeout(TimeSpan.FromSeconds(5));

            if (!string.IsNullOrEmpty(_eegModel.bluetoothManager.ConnectedDeviceAddress)) {
                this.SendCommand<EEGConnectedCommand>();
                return;
            }

            await ConnectToDevice(deviceAddress, _connectionCts.Token);
        } catch (OperationCanceledException) {
            Debug.Log("ConnectToDevice was cancelled");
        } catch (Exception ex) {
            Debug.LogError($"ConnectToDevice Connection failed: {ex.Message}");
            _eegModel.SetEegStatus(EegStatusEnum.Error);
            this.SendCommand<ConnectBleEegCommand>();
        } finally {
            _connectionCts.Dispose();
            _connectionCts = null;
        }
    }

    private async UniTask ConnectToDevice(string address, CancellationToken ct) {
        Debug.Log("ConnectWithRetry");
        while (_connectionCts != null && !ct.IsCancellationRequested) {
            _eegModel.SetEegStatus(EegStatusEnum.Connecting);
            Debug.Log($"Connecting to {address}");
            _eegModel.bluetoothManager.ConnectBle(address);

            await UniTask.WaitUntil(
                () => !string.IsNullOrEmpty(_eegModel.bluetoothManager.ConnectedDeviceAddress),
                cancellationToken: ct
            ).Timeout(TimeSpan.FromSeconds(5));

            if (!string.IsNullOrEmpty(_eegModel.bluetoothManager.ConnectedDeviceAddress)) {
                this.SendCommand<EEGConnectedCommand>();
                return;
            }
        }

        // 如果走到这里，说明重试被取消或未成功
        _eegModel.SetEegStatus(EegStatusEnum.Error);
    }

    #endregion

    #region Data Handling

    private void OnRefreshFoundDevices() {
        if (!string.IsNullOrEmpty(_eegModel.bluetoothManager.ConnectedDeviceAddress)) {
            _eegModel.deviceInfo.Address = _eegModel.bluetoothManager.ConnectedDeviceAddress;
            _eegModel.deviceInfo.Name = _eegModel.bluetoothManager.FoundDeviceDic[_eegModel.deviceInfo.Address].Name;

            GetDeviceInfo();
            StartCoroutine(WaitForDataCollection());
        }
    }

    private IEnumerator WaitForDataCollection() {
        yield return new WaitForSeconds(3f);
        _isGetRawData = false;
        _eegModel.enterTechBleManager.WriteStartCollectCommand();
    }

    private void OnEegDataReceived(string rawData) {
        if (!_eegModel.isActive.Value) return;

        if (!_isGetRawData) {
            _isGetRawData = true;
            Debug.Log("EEG started receiving data");
        }

        if (_eegModel.IsRecording) _dataBuffer.AppendLine($"{DateTime.Now:HHmmssfff}:{rawData.Clone()}");
    }

    public void StartRecord() {
        if (Application.platform != RuntimePlatform.Android) return;
        if (!_eegModel.isActive.Value) return;

        Debug.Log("EEG recording started");
        _eegModel.IsRecording = true;
        _dataBuffer.Clear();
    }

    public void StopRecord(string filePath = null) {
        if (Application.platform != RuntimePlatform.Android) return;
        if (!_eegModel.isActive.Value) return;

        print("EEG, StopRecord");
        if (_eegModel.IsRecording) {
            _eegModel.IsRecording = false;
            //EnterTechBleManager.Instance.WriteStopCollectCommand();  // 不能停止，停止的话，就不能看到 IsTouchSkin 的信息了
            if (filePath == null) return;
            File.WriteAllText(filePath, _dataBuffer.ToString());
            print("保存EEG 到：" + filePath);
            _dataBuffer.Clear();
        }
    }

    #endregion

    #region Device Info

    private void OnGetBatteryRequested(GetEegBatteryEvent e) {
        if (_eegModel.eegStatus.Value != EegStatusEnum.Connected &&
            _eegModel.eegStatus.Value != EegStatusEnum.NoForehead) return;

        Debug.Log("GetBattery requested");
        _eegModel.enterTechBleManager.OnUpdateUpdateBatteryInfoCB += OnBatteryInfoUpdated;

        DelayReadBatteryData();
    }

    private void OnBatteryInfoUpdated(double voltage, uint batteryLevel) {
        _eegModel.enterTechBleManager.OnUpdateUpdateBatteryInfoCB -= OnBatteryInfoUpdated;
        _eegModel.deviceInfo.BatteryLevel = $"{batteryLevel}%";
        this.SendCommand<ShowEegBatteryCommand>();
    }

    private void GetDeviceInfo() {
        DelayReadBatteryData();
        DelayReadFirmwareInfoData();
        DelayReadHardwareInfoData();
    }

    #endregion

    #region enterTechBleManager 私有函数

    private void DelayReadBatteryData() {
        _eegModel.enterTechBleManager.ReadCharacteristic(_eegModel.bluetoothManager.ConnectedDeviceAddress,
            EnterTechBleManager.BATTERY, EnterTechBleManager.BATTERY_LEVEL);
    }

    private void DelayReadFirmwareInfoData() {
        _eegModel.enterTechBleManager.ReadCharacteristic(_eegModel.bluetoothManager.ConnectedDeviceAddress,
            EnterTechBleManager.DEVICE_INFO,
            EnterTechBleManager.DEVICE_FIRMWARE);
    }

    private void DelayReadHardwareInfoData() {
        _eegModel.enterTechBleManager.ReadCharacteristic(_eegModel.bluetoothManager.ConnectedDeviceAddress,
            EnterTechBleManager.DEVICE_INFO,
            EnterTechBleManager.DEVICE_HARDWARE);
    }

    #endregion
}
