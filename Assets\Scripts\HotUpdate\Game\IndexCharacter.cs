using QFramework;
using UnityEngine;

public class IndexCharacter : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IController {
    public GameObject Character;
    public MCharacterStyle mCharacterStyle;

    private void Awake() {
        this.GetSystem<IIndexCharacterSystem>().MMCharacterStyle = mCharacterStyle;
        this.GetSystem<IIndexCharacterSystem>().Character = Character;
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }
}
