﻿using System;
using System.Collections;
using System.Collections.Generic;
using QFramework;
using UnityEngine;
using UnityEngine.UI;
using Utils;

public class WarningAlertInfo {
    public Action callback;
    public string text;
    public float time;

    public WarningAlertInfo(string content, float t, Action action) {
        text = content;
        time = t;
        callback = action;
    }

    public WarningAlertInfo(string content) {
        text = content;
        time = 1;
        callback = null;
    }

    public WarningAlertInfo(string content, Action action) {
        text = content;
        time = 1;
        callback = action;
    }
}

public class WarningAlert : UIPenal {
    public Text warningText;
    private readonly Queue<WarningAlertInfo> queue = new();
    private Coroutine warningCor;

    public override void InitData(object data) {
        var info = data as WarningAlertInfo;
        if (info == null) return;
        ShowWithText(info.text, info.time, info.callback);
    }

    private void ShowWithText(string text, float time = 1.0f, Action callback = null) {
        var info = new WarningAlertInfo(text, time, callback);
        queue.Enqueue(info);
        gameObject.SetActiveFast(true);
        if (warningCor == null) warningCor = StartCoroutine(HideWarningAlert());
    }

    private IEnumerator HideWarningAlert() {
        while (queue.Count != 0) {
            var info = queue.Dequeue();
            var content = this.GetSystem<II18NSystem>().GetText(info.text);
            if (content == "") content = info.text;
            warningText.text = content;
            yield return new WaitForSeconds(info.time);
            info.callback?.Invoke();
        }

        warningCor = null;
        gameObject.SetActiveFast(false);
    }
}
