using UnityEngine;

public class IndexCameraController : MonoBehaviour {
    public float cameraRotateSpeed = 5;
    public Transform characterRoot;
    private float x;

    private void Update() {
#if UNITY_STANDALONE || UNITY_WEBGL || UNITY_EDITOR

        if (Input.GetMouseButton(0) && Input.mousePosition.y > Screen.height / 3) {
            x = Mathf.Lerp(x, Mathf.Clamp(Input.GetAxis("Mouse X"), -2, 2) * cameraRotateSpeed, Time.deltaTime * 5.0f);
            Camera.main.fieldOfView = Mathf.Clamp(Camera.main.fieldOfView, 50, 60);
            Camera.main.fieldOfView = Mathf.Lerp(Camera.main.fieldOfView, 50, Time.deltaTime);
            transform.RotateAround(characterRoot.position, Vector3.up, x);
        }

#elif UNITY_ANDROID||UNITY_IOS
        if (Input.touchCount == 1 && Input.GetTouch(0).position.y>(Screen.height / 3))
        {
            switch (Input.GetTouch(0).phase)
            {
                case TouchPhase.Moved:
                    x =
 Mathf.Lerp(x, Mathf.Clamp(Input.GetTouch(0).deltaPosition.x, -2, 2) * cameraRotateSpeed, Time.deltaTime*3.0f);
                    Camera.main.fieldOfView = Mathf.Clamp(Camera.main.fieldOfView, 50, 60);
                    Camera.main.fieldOfView = Mathf.Lerp(Camera.main.fieldOfView, 50, Time.deltaTime);
                    break;
            }
        transform.RotateAround(characterRoot.position, Vector3.up, x);
        }
#endif
    }
}
