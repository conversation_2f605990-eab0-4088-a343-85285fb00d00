﻿using QFramework;

public class ApplyCharacterCommand : AbstractCommand {
    private readonly CharacterInfo _characterInfo;

    public ApplyCharacterCommand(CharacterInfo characterInfo) {
        _characterInfo = characterInfo;
    }

    protected override void OnExecute() {
        this.GetModel<IUserModel>().Cid.Value = _characterInfo.cid;
        var alertInfo = new WarningAlertInfo("Successfully Set");
        this.SendEvent(new ShowPageEvent(UIPageType.WarningAlert, UILevelType.Alart, alertInfo));
        this.SendEvent<ApplyCharacterEvent>();
    }
}
