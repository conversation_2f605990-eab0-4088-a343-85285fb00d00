using System;
using System.IO;
using System.Linq;
using System.Threading;
using Cysharp.Threading.Tasks;
using QFramework;
using UnityEngine;
using Utils;

public class AsyncLogWriter : MonoBehaviour, IController {
    private readonly ManualResetEvent logEvent = new(false);
    private readonly ConcurrentQueue<string> logQueue = new();
    private string fullPath;
    private bool isRunning = true;
    private ILogModel logModel;
    private Thread logThread;
    private string targetDir;

    private ITaskModel taskModel;

    private void Awake() {
        // 确保单例
        if (FindObjectsOfType<AsyncLogWriter>().Length > 1) {
            Destroy(gameObject);
            return;
        }

        DontDestroyOnLoad(gameObject);

        logModel = this.GetModel<ILogModel>();
        taskModel = this.GetModel<ITaskModel>();
        this.RegisterEvent<UploadLogEvent>(CreateNewLogFile).UnRegisterWhenGameObjectDestroyed(gameObject);
    }

    private void OnApplicationQuit() {
        AbortLogThread();
        Application.logMessageReceivedThreaded -= HandleLog;
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    private void HandleLog(string logString, string stackTrace, LogType type) {
        if (logString.Contains("ListenContactData") ||
            logString.Contains("DTC.EnterTechBleManager") ||
            logString.Contains("设备配置是否正常") ||
            logString.Contains("DTC") ||
            logString.Contains("OnListenBatteryCallBack") ||
            logString.Contains("BluetoothDeviceScript")) return;

        var formattedLog = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] [{type}] {logString}\n{stackTrace}";
        logQueue.Enqueue(formattedLog);
        logEvent.Set(); // 唤醒处理线程
    }

    private void ProcessLogs() {
        try {
            using (var writer = new StreamWriter(fullPath, true)) {
                while (isRunning) {
                    // 处理所有现有日志
                    while (logQueue.TryDequeue(out var log)) {
                        writer.WriteLine(log);
                        writer.Flush(); // 确保实时写入
                    }

                    // 等待新日志或超时
                    logEvent.WaitOne(100);
                    logEvent.Reset();
                }

                // 退出前写入剩余日志
                while (logQueue.TryDequeue(out var log)) writer.WriteLine(log);
            }
        } catch (Exception e) {
            Debug.LogError($"日志写入失败: {e}");
        }
    }

    private void AbortLogThread() {
        isRunning = false;
        logEvent.Set(); // 确保线程退出等待

        if (logThread != null && logThread.IsAlive) {
            logThread.Join(1000); // 等待最多1秒
            if (logThread.IsAlive)
                logThread.Abort();
        }
    }

    #region 日志保存与上传

    private async void Start() {
        Application.logMessageReceivedThreaded += HandleLog;

        Debug.Log("InitLog");
        await UploadUnsentLogs();
        CreateTargetDir();
        SetNewFullPath();
    }

    private void CreateTargetDir() {
        Debug.Log("CreateTargetDir");
        targetDir = Path.Combine(Util.LogDir,
            this.GetModel<IUserModel>().Uid + "-" + this.GetModel<IUserModel>().Name.Value,
            DateTime.Now.ToString("yyyy-MM-dd"));
        if (!Directory.Exists(targetDir)) Directory.CreateDirectory(targetDir);
    }

    private void SetNewFullPath() {
        Debug.Log("SetNewFullPath");
        var dateTime = DateTime.Now.ToString("HHmmss");
        var fileName = $"LOG_FILE-{dateTime}.txt"; // 初始进入时，没有任务信息，此处文件名只标识时间信息
        fullPath = Path.Combine(targetDir, Util.ToValidFileName(fileName));
        logModel.UnUploadLogList.Add(fullPath);
        PlayerPrefs.SetString(PrefKeys.UnUploadLogList, string.Join("*", logModel.UnUploadLogList));

        logThread = new Thread(ProcessLogs);
        logThread.IsBackground = true;
        logThread.Start();
    }

    // 任务完成触发，创建新日志，上传旧日志
    private async void CreateNewLogFile(UploadLogEvent e) {
        AbortLogThread();
        await UniTask.Delay(1000);
        var oldFullPath = fullPath;
        SetNewFullPath();
        var dateTime = DateTime.Now.ToString("HHmmss");
        var fileName = $"LOG_FILE-{dateTime}-{e.m_taskInfo}.txt";

        var newFilePath = Path.Combine(targetDir, fileName);
        Util.RenameFile(oldFullPath, newFilePath);
        logModel.UnUploadLogList.Remove(oldFullPath);
        logModel.UnUploadLogList.Add(newFilePath);
        PlayerPrefs.SetString(PrefKeys.UnUploadLogList, string.Join("*", logModel.UnUploadLogList));

        await UploadLog(newFilePath);
    }

    /// <summary>
    ///     上传日志到服务端
    ///     1. logFilePath 不是全局，可能是未上传的
    ///     2. 目标路径文件名 target 相关，可以为null，代表之前未上传
    /// </summary>
    private async UniTask UploadLog(string logFilePath) {
        var result = await this.GetSystem<INetworkSystem>().UploadFileAsync(
            logFilePath,
            RequestUrl.UpdateLogUrl,
            mimeType: "text/plain",
            token: this.GetModel<IUserModel>().Token.Value);

        if (result.Success) {
            Debug.Log("日志上传完毕: " + logFilePath);
            // 将此日志从未上传的列表中移除
            logModel.UnUploadLogList.Remove(logFilePath);
            PlayerPrefs.SetString(PrefKeys.UnUploadLogList, string.Join("*", logModel.UnUploadLogList));
        } else {
            Debug.LogError($"UploadLog, {result.ErrorMessage} {result.StatusCode}");
        }
    }

    private async UniTask UploadUnsentLogs() {
        Debug.Log("unsentLogFilePath :" + string.Join("*", logModel.UnUploadLogList));
        foreach (var unsentLogFilePath in logModel.UnUploadLogList.ToList())
            if (File.Exists(unsentLogFilePath)) {
                await UploadLog(unsentLogFilePath);
                // 无论是否上传成功，都从列表中移除。避免一直异常上传
                logModel.UnUploadLogList.Remove(unsentLogFilePath);
                PlayerPrefs.SetString(PrefKeys.UnUploadLogList, string.Join("*", logModel.UnUploadLogList));
            }
    }

    #endregion
}
