﻿using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Utils;

public class GuidanceEventPenetrate : MonoBehaviour, IPointerClickHandler {
    public int index;
    public int maxIndex;
    public float shrinkTime;
    private bool _isCanClick;

    private Image _targetImage;
    private bool clicking;

    private bool hasPassedEvent;
    private Canvas targetCanvas;

    public void OnPointerClick(PointerEventData eventData) {
        if (RectTransformUtility.RectangleContainsScreenPoint(_targetImage.rectTransform, eventData.position))
            if (!clicking) {
                clicking = true;
                Util.DelayExecuteWithSecond(shrinkTime, () => { clicking = false; });
                index = Mathf.Min(index + 1, maxIndex);
                if (_isCanClick) Psss(eventData, ExecuteEvents.pointerClickHandler);
            }
    }

    public void SetTargetImage(Image target, bool isCanClick, Canvas canvas) {
        _targetImage = target;
        _isCanClick = isCanClick;
        targetCanvas = canvas;
    }

    public void Psss<T>(PointerEventData data, ExecuteEvents.EventFunction<T> function)
        where T : IEventSystemHandler {
        if (hasPassedEvent) return;
        hasPassedEvent = true;
        var results = new List<RaycastResult>();
        EventSystem.current.RaycastAll(data, results);
        var current = data.pointerCurrentRaycast.gameObject;

        for (var i = 0; i < results.Count; i++)
            if (current != results[i].gameObject) {
                Debug.Log("++++ GuidanceEventPenetrate " + results[i].gameObject + "  " + i);
                if (ExecuteEvents.Execute(results[i].gameObject, data, function)) break;
            }

        results.Clear();
        hasPassedEvent = false;
    }
}
