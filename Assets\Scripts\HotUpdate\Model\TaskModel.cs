﻿using System;
using System.Collections.Generic;
using LitJson;
using QFramework;

public enum TaskType {
    chat = 0,
    name,
    DDemanding
}

public class TaskInfo {
    public int day;
    public string llmName;
    public int id;
    public string name;
    public string prompt;
    public int tid;
    public int time; // 设定任务时间
    public TaskType typeId;
}

/// <summary>
///     指标分析结果
/// </summary>
public class TaskMetrics {
    public float averageLLMResponseTime; // LLM平均响应速度（单位：秒）
    public float averageSentenceLength; // 平均句长（字/句）
    public float averageSTTResponseTime; // STT平均响应速度（单位：秒）
    public float averageThinkingInterval; // 儿童平均思考间隔（秒）
    public float averageTTSResponseTime; // TTS平均响应速度（单位：秒）

    // 基础指标
    // 程序统计
    public int dialogueTurns; // 对话轮次

    // 统计计算用
    public float dialogueValidTime; // 对话有效时间（秒） 暂停时间未计算在内
    public float eegValidTime; // 脑电数据记录有效时间（秒） 对话时间内的正确佩戴时间
    public float eegValidTimeRatio; // 脑电数据记录有效时间占比（百分比）

    // AI或工具统计
    public List<string> highFrequencyWords; // 高频重复词汇（需JSON序列化）
    public float maxThinkingInterval; // 最长思考间隔（秒）
    public int nounCount; // 名词个数

    // 实验维度
    public float progressPercentage; // 实验进度（0-100%）

    public int ResponseTurns; // AI回应轮次（正常情况下）
    public int STTTurns; // STT轮次（正常情况下）

    public float totalLLMTime; // 总LLM时间
    public float totalSentenceLength; // 总句长
    public float totalSTTTime; // 总STT时间
    public float totalThinkingTime; // 总思考时间
    public float totalTTSTime; // 总TTS时间
    public int TTSTurns; // TTS回应轮次（正常情况下）
    public int verbCount; // 动词个数
    public float verbNounRatio; // 动名词占比
}

public interface ITaskModel : IModel {
    public TaskInfo taskInfo { get; set; }
    public float completionDegree { get; set; } // 任务完成度 ，对话时间占要求时间的比例，达到此比例，认为任务完成
    public TaskMetrics taskMetrics { get; set; }
}

public class TaskModel : AbstractModel, ITaskModel {
    public TaskInfo taskInfo { get; set; } = new();
    public float completionDegree { get; set; } = 0.8f;
    public TaskMetrics taskMetrics { get; set; } = new();

    protected override void OnInit() {
    }
}

// 响应数据模型
[Serializable]
public class Response {
    public int code;
    public JsonData data;
}

// uploadChatHistory 请求数据模型
[Serializable]
public class ChatHistoryRequest {
    public int uid;
    public int id;
    public string fileData;
    public bool exception;
}

[Serializable]
public class LoginRequest {
    public string username;
    public string password;
}

[Serializable]
public class UpdateUserPasswordRequest {
    public string password;
}

[Serializable]
public class BuyAvatarRequest {
    public int aid;
}

[Serializable]
public class UserLoginRecordRequest {
    public long id;
    public long uid;
    public string device;
    public string place;
    public DateTime loginTime;
}

[Serializable]
public class SetTaskIsCompleteRequest {
    public int id;
}

[Serializable]
public class QuestionnaireResultRequest {
    public string result;
}

[Serializable]
public class UserInfoRequest {
    public int uid;
}
