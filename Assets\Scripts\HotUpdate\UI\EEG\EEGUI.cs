using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using Cysharp.Threading.Tasks;
using DTC;
using QFramework;
using UnityEngine;
using UnityEngine.UI;
using Utils;

public class EEGUI : MonoBehaviour, IController {
    private const int CorrectTime = 60; // 校准时间，单位：秒

    // UI Elements
    [Header("Connection Panel")] public GameObject panel1;

    public ListView list;
    public GameObject skin;
    public Button scanBtn;

    [Header("Device Info Panel")] public GameObject panel2;

    public Text deviceNameText;
    public Text deviceBatteryText;
    public Text deviceAddressText;
    public Button unBandBtn;
    public Button eegCorrectBtn;

    [Header("Calibration Panel")] public GameObject panel3;

    public Text correctTip1Text;
    public Text correctTip2Text;
    public Slider timeSlider;
    public Button correctBtn;

    [Header("Calibration Complete Panel")] public GameObject panel4;

    public Button startTrainBtn;

    public Button closeBtn;

    private readonly Queue<string> _eegDataQueue = new();
    private BluetoothEEG _bluetoothEeg;
    private BluetoothManager _bluetoothManager;

    private CancellationTokenSource _cts;
    private IEegModel _eegModel;
    private EnterTechBleManager _enterTechBleManager;
    private Text correctBtnText;

    private void Awake() {
        _eegModel = this.GetModel<IEegModel>();
        _bluetoothManager = _eegModel.bluetoothManager;
        _enterTechBleManager = _eegModel.enterTechBleManager;

        if (Application.platform == RuntimePlatform.Android)
            _bluetoothEeg = GameObject.Find("BluetoothEEG").GetComponent<BluetoothEEG>();

        InitializeUIComponents();
        RegisterEvents();
    }

    private void OnEnable() {
        UpdateUiState(_eegModel.eegStatus.Value);
    }

    private void OnDisable() {
        _cts?.Cancel();
    }

    private void OnDestroy() {
        _cts?.Dispose();
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    private void InitializeUIComponents() {
        list.setSkin = skin.GetComponent<RectTransform>();
        list.OnUpdateItem += OnDeviceItemUpdated;
        list.m_left = 20;
        list.m_top = 20;
        list.m_down = 30;

        scanBtn.onClick.AddListener(OnScanClicked);
        unBandBtn.onClick.AddListener(OnUnbindClicked);
        closeBtn.onClick.AddListener(OnCloseClicked);
        eegCorrectBtn.onClick.AddListener(OnEegCorrectClicked);
        correctBtn.onClick.AddListener(OnCorrectClicked);
        startTrainBtn.onClick.AddListener(OnStartTrainClicked);

        correctBtnText = correctBtn.GetComponentInChildren<Text>();
    }

    private void RegisterEvents() {
        this.RegisterEvent<ShowEegBatteryEvent>(OnEegBatteryUpdated)
            .UnRegisterWhenGameObjectDestroyed(gameObject);
        this.RegisterEvent<EEGCalibrationDoneEvent>(OnCalibrationDone)
            .UnRegisterWhenGameObjectDestroyed(gameObject);
        this.RegisterEvent<EEGConnectedEvent>(e => { UpdateUiState(EegStatusEnum.Connected); })
            .UnRegisterWhenGameObjectDestroyed(gameObject);
    }

    private void UpdateUiState(EegStatusEnum newState) {
        if (!gameObject.activeSelf) {
            _eegModel.eegStatus.Value = newState;
            return;
        }

        // Update UI visibility
        panel1.SetActiveFast(newState == EegStatusEnum.IsUnBindDevice ||
                             newState == EegStatusEnum.Scanning ||
                             newState == EegStatusEnum.Disconnected ||
                             newState == EegStatusEnum.Connecting);
        panel2.SetActiveFast(newState == EegStatusEnum.Connected ||
                             newState == EegStatusEnum.NoForehead);
        panel3.SetActiveFast(false);
        panel4.SetActiveFast(false);

        // Enter new state
        switch (newState) {
            case EegStatusEnum.Scanning:
                StartCoroutine(ShowDevicesCoroutine());
                break;
            case EegStatusEnum.Connected:
                StartCoroutine(ShowDeviceInfoCoroutine());
                break;
            case EegStatusEnum.NoForehead:
                StartCoroutine(ShowDeviceInfoCoroutine());
                break;
        }

        _eegModel.eegStatus.Value = newState;
    }

    #region Button Handlers

    private void OnScanClicked() {
        this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
        if (_bluetoothManager.GetBindDevice() == null) {
            _bluetoothManager.Scan();
        } else {
            if (_eegModel.eegStatus.Value == EegStatusEnum.Connected ||
                _eegModel.eegStatus.Value == EegStatusEnum.NoForehead)
                UpdateUiState(EegStatusEnum.Connected);
            else
                this.SendCommand(new ConnectBleEegCommand());
        }

        UpdateUiState(EegStatusEnum.Scanning);
    }

    private void OnUnbindClicked() {
        this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
        _enterTechBleManager.WriteStopCollectCommand();
        _bluetoothManager.DisConnectBle(_bluetoothManager.GetBindDevice());
        _bluetoothManager.UnBindDevice();
        UpdateUiState(EegStatusEnum.IsUnBindDevice);
    }

    private void OnCloseClicked() {
        this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
        gameObject.SetActiveFast(false);
    }

    private void OnEegCorrectClicked() {
        this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
        panel2.SetActiveFast(false);
        panel3.SetActiveFast(true);
        correctBtnText.text = "开始校准";
        correctTip1Text.text = "请将佩戴好脑机设备，\n然后点击开始校准...";
        correctTip2Text.gameObject.SetActiveFast(false);
        timeSlider.gameObject.SetActiveFast(false);
    }

    private void OnCorrectClicked() {
        this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);

        if (correctBtnText.text == "开始校准")
            StartCalibration();
        else if (correctBtnText.text == "中断校准") StopCalibration();
    }

    private void OnStartTrainClicked() {
        this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
        this.SendCommand(new HidePageCommand(UIPageType.EEGBluetoothUI));
        UpdateUiState(EegStatusEnum.Connected);
    }

    #endregion

    #region Calibration Logic

    private void StartCalibration() {
        if (_eegModel.eegStatus.Value != EegStatusEnum.Connected) {
            var text = _eegModel.FixEeg();
            var info = new InfoConfirmInfo(
                content: $"{text}，\n再进行校准！",
                type: ConfirmAlertType.Single);
            this.SendCommand(new ShowPageCommand(UIPageType.InfoConfirmAlert, UILevelType.Alart, info));
            return;
        }

        correctTip1Text.text = "正在校准...";
        correctTip2Text.text = $"请保持注视十字线  <color=#00ff00>{CorrectTime}秒</color>";
        correctBtnText.text = "中断校准";
        correctTip2Text.gameObject.SetActiveFast(true);
        closeBtn.gameObject.SetActiveFast(false);
        timeSlider.gameObject.SetActiveFast(true);

        _cts = new CancellationTokenSource();
        StartCoroutine(CountdownTimerCoroutine());
        _bluetoothEeg.StartRecord();
    }

    private void StopCalibration() {
        _eegModel.IsRecording = true;
        _bluetoothEeg.StopRecord();
        _cts?.Cancel();
        closeBtn.gameObject.SetActiveFast(true);
        UpdateUiState(EegStatusEnum.Connected);
    }

    private IEnumerator CountdownTimerCoroutine() {
        Debug.Log("IEnumerator 开始校准...");
        float remainingTime = CorrectTime;
        timeSlider.value = 0;

        while (remainingTime > 0 && !_cts.IsCancellationRequested) {
            if (_eegModel.eegStatus.Value == EegStatusEnum.Connected) {
                correctTip1Text.text = "正在校准...";
                _eegModel.IsRecording = true;
                remainingTime -= Time.deltaTime;
                remainingTime = Mathf.Max(remainingTime, 0);
                timeSlider.value = CorrectTime - remainingTime;
            } else {
                correctTip1Text.text = "<b><color=red>暂停中!请佩戴好设备!</color></b>";
                _eegModel.IsRecording = false;
            }

            yield return null;
        }

        if (remainingTime <= 0) this.SendCommand<EEGCalibrationDoneCommand>();
    }

    private async void OnCalibrationDone(EEGCalibrationDoneEvent e) {
        var fileName = $"{DateTime.Now:HHmmss}_CalibrationEEGData.txt";
        _eegModel.CalibrationEEGDataFilePath = Path.Combine(Util.DateDir, fileName);
        PlayerPrefs.SetString(PrefKeys.CalibrationEEGDataFilePath, _eegModel.CalibrationEEGDataFilePath);

        _eegModel.IsRecording = true;
        _bluetoothEeg.StopRecord(_eegModel.CalibrationEEGDataFilePath);
        _eegModel.isCorrected.Value = true;

        await UploadEEGData(_eegModel.CalibrationEEGDataFilePath);

        closeBtn.gameObject.SetActiveFast(true);
        panel3.SetActiveFast(false);
        panel4.SetActiveFast(true);
    }

    private async UniTask UploadEEGData(string filePath) {
        if (!File.Exists(filePath)) {
            Debug.LogError("File does not exist: " + filePath);
            return;
        }

        var result = await this.GetSystem<INetworkSystem>().UploadFileAsync(
            filePath,
            RequestUrl.UploadEEGUrl,
            mimeType: "text/plain",
            token: this.GetModel<IUserModel>().Token.Value);

        if (result.Success)
            Debug.Log("EEG 校准数据 上传完毕");
        else
            Debug.LogError($"UploadEEGUrl, {result.ErrorMessage} {result.StatusCode}");
    }

    #endregion

    #region Device Management

    private IEnumerator ShowDevicesCoroutine() {
        list.setData(new List<ItemData>());
        yield return new WaitUntil(() => _bluetoothManager.FoundDeviceDic.Count > 0);

        var devices = new List<ItemData>();
        var index = 0;

        foreach (var device in _bluetoothManager.FoundDeviceDic)
            devices.Add(new ItemData {
                Name = $"{device.Value.Name}：{device.Value.Address}",
                Index = index++
            });

        list.setData(devices);
    }

    private void OnDeviceItemUpdated(Item item) {
        var text = item.m_childName["Text"].GetComponent<Text>();
        var button = item.m_childName["Button"].GetComponent<Button>();

        var name = (item.data as ItemData).Name;
        text.text = name;

        var parts = name.Split('：');
        if (parts.Length != 2) throw new FormatException($"Invalid device name format: {name}");

        button.onClick.RemoveAllListeners();
        button.onClick.AddListener(() => ConnectToDevice(parts[1]));
    }

    private async void ConnectToDevice(string address) {
        UpdateUiState(EegStatusEnum.Connecting);

        try {
            _bluetoothManager.ConnectBle(address);
            PlayerPrefs.SetString(PrefKeys.ConnectedEEGDeviceAddress, address);

            await UniTask.WaitUntil(() =>
                        !string.IsNullOrEmpty(_bluetoothManager.ConnectedDeviceAddress),
                    cancellationToken: this.GetCancellationTokenOnDestroy())
                .Timeout(TimeSpan.FromSeconds(5));

            UpdateUiState(EegStatusEnum.Connected);
        } catch (Exception ex) {
            Debug.LogError($"Connection failed: {ex.Message}");
            UpdateUiState(EegStatusEnum.Disconnected);
            this.SendCommand<ConnectBleEegCommand>();
        }
    }

    private IEnumerator ShowDeviceInfoCoroutine() {
        string addr = null;
        yield return new WaitUntil(() => {
            addr = _bluetoothManager.ConnectedDeviceAddress; // 直接缓存
            return !string.IsNullOrEmpty(addr);
        });

        string name;
        // 出现过 addr 不为空，但是 没有对应 name 为空的情况
        // 此时，先断开连接，再自动连接
        if (_bluetoothManager.FoundDeviceDic.TryGetValue(addr, out var value)) {
            name = value.Name;
        } else {
            _bluetoothManager.DisConnectBle(_bluetoothManager.GetBindDevice());
            this.SendCommand<ConnectBleEegCommand>();
            yield break;
        }

        deviceNameText.text = name;
        deviceAddressText.text = addr;
        if (string.IsNullOrEmpty(deviceBatteryText.text)) deviceBatteryText.text = "获取中...";

        this.SendCommand<GetEegBatteryCommand>();
        yield return new WaitForSeconds(2);
        deviceBatteryText.text = _eegModel.deviceInfo.BatteryLevel;
    }

    private void OnEegBatteryUpdated(ShowEegBatteryEvent e) {
        deviceBatteryText.text = _eegModel.deviceInfo.BatteryLevel;
    }

    #endregion
}

public class ItemData {
    public string Name { get; set; }
    public int Index { get; set; }
}
