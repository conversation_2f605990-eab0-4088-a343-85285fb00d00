using QFramework;
using UnityEngine.AddressableAssets;

public class LoadSceneCommand : AbstractCommand {
    private readonly string mSceneID;

    public LoadSceneCommand(string sceneID) {
        mSceneID = sceneID;
    }

    protected override void OnExecute() {
        if (this.GetModel<IGameModel>().SceneLoading.Value) return;
        this.GetModel<IGameModel>().LoadingTargetSceneID.Value = mSceneID;
        Addressables.LoadSceneAsync(ScenePath.Loading);
    }
}
