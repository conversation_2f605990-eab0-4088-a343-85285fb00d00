﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using QFramework;
using UnityEngine;
using UnityEngine.ResourceManagement.AsyncOperations;
using UnityEngine.UI;
using Utils;

[Serializable]
public class TaskResultUI : Mono<PERSON><PERSON><PERSON><PERSON>, IController {
    [SerializeField] public Text taskNameText;
    [SerializeField] public Text userNameText;
    [SerializeField] public Image userAvatarImage;
    [SerializeField] public Button nextTaskBtn;
    [SerializeField] public Button quitTaskBtn;

    [SerializeField] public Slider scheduleSlider;
    [SerializeField] public Text scheduleText;
    public Text dialogueTurnsText; // 对话轮次
    public Text averageThinkingIntervalText; // 儿童平均思考间隔（秒）
    public Text maxThinkingIntervalText; // 最长思考间隔（秒）
    public Text averageSentenceLengthText; // 平均句长（字/句）

    public Text eegValidTimeRatioText; // 脑电数据记录有效时间占比（百分比）

    // public Text averageLLMResponseTimeText; // LLM平均响应速度（单位：秒）
    // public Text averageSTTResponseTimeText; // STT平均响应速度（单位：秒）
    // public Text averageTTSResponseTimeText; // TTS平均响应速度（单位：秒）
    public Text nounCountText; // 名词个数
    public Text verbCountText; // 动词个数
    public Text verbNounRatioText; // 动名词占比

    public List<GameObject> stars;
    private ITaskModel taskModel;

    public void Start() {
        nextTaskBtn.onClick.AddListener(() => {
            Debug.Log("nextTaskBtn.onClick");
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            DisableStar();
            gameObject.SetActiveFast(false);
            this.SendCommand(new UpdateTaskCommand(() => this.SendCommand(new TaskStartCommand())));
        });

        quitTaskBtn.onClick.AddListener(() => {
            Debug.Log("quitTaskBtn.onClick");
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            DisableStar();
            gameObject.SetActiveFast(false);
            this.SendCommand(new TaskQuitCommand());
        });
    }


    public async void OnEnable() {
        taskModel = this.GetModel<ITaskModel>();
        taskNameText.text = taskModel.taskInfo.name;
        userNameText.text = this.GetModel<IUserModel>().Name.Value;

        var result = await this.GetSystem<INetworkSystem>().PostJson<ScheduleResponse>(
            RequestUrl.getTaskScheduleUrl,
            token: this.GetModel<IUserModel>().Token.Value);

        // 处理响应
        if (result.IsSuccess) {
        } else {
            Debug.LogError($"getTaskScheduleUrl Erroor: [{result.StatusCode}]: {result.ErrorMessage}");
        }

        var schedule = result.IsSuccess ? result.Data.schedule : 0f;
        StatTaskMetrics(schedule);
        ShowTaskMetrics();

        var obj = await this.GetSystem<IAddressableSystem>()
            .LoadAssetAsync<Sprite>(Util.GetAvatarUrl(this.GetModel<IUserModel>().Aid.Value));
        if (obj.Status == AsyncOperationStatus.Succeeded)
            userAvatarImage.sprite = Instantiate(obj.Result, transform, false);
        // StartCoroutine(ShowStar());
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    private void StatTaskMetrics(float schedule) {
        taskModel.taskMetrics.eegValidTimeRatio = taskModel.taskMetrics.dialogueValidTime != 0
            ? (float)Math.Round(taskModel.taskMetrics.eegValidTime / taskModel.taskMetrics.dialogueValidTime, 2)
            : 0f;

        taskModel.taskMetrics.averageLLMResponseTime = taskModel.taskMetrics.ResponseTurns != 0
            ? (float)Math.Round(taskModel.taskMetrics.totalLLMTime / taskModel.taskMetrics.ResponseTurns, 2)
            : 0f;

        taskModel.taskMetrics.averageSTTResponseTime = taskModel.taskMetrics.STTTurns != 0
            ? (float)Math.Round(taskModel.taskMetrics.totalSTTTime / taskModel.taskMetrics.STTTurns, 2)
            : 0f;

        taskModel.taskMetrics.averageTTSResponseTime = taskModel.taskMetrics.TTSTurns != 0
            ? (float)Math.Round(taskModel.taskMetrics.totalTTSTime / taskModel.taskMetrics.TTSTurns, 2)
            : 0f;

        taskModel.taskMetrics.averageThinkingInterval = taskModel.taskMetrics.dialogueTurns != 0
            ? (float)Math.Round(taskModel.taskMetrics.totalThinkingTime / taskModel.taskMetrics.dialogueTurns, 2)
            : 0f;

        taskModel.taskMetrics.averageSentenceLength = taskModel.taskMetrics.dialogueTurns != 0
            ? (float)Math.Round(taskModel.taskMetrics.totalSentenceLength / taskModel.taskMetrics.dialogueTurns, 2)
            : 0f;

        taskModel.taskMetrics.progressPercentage = schedule;

        var result = Util.CountNounsAndVerbs(this.GetModel<IChatModel>().GetChatHistory());

        taskModel.taskMetrics.nounCount = result.Item1;
        taskModel.taskMetrics.verbCount = result.Item2;
        taskModel.taskMetrics.verbNounRatio = result.Item3 != 0
            ? (float)Math.Round((float)(result.Item1 + result.Item2) / result.Item3, 2)
            : 0f;
    }

    private void ShowTaskMetrics() {
        scheduleSlider.value = taskModel.taskMetrics.progressPercentage;
        scheduleText.text = taskModel.taskMetrics.progressPercentage * 100 + "%";

        dialogueTurnsText.text = taskModel.taskMetrics.dialogueTurns + "轮";
        averageThinkingIntervalText.text = taskModel.taskMetrics.averageThinkingInterval.ToString("0.00") + "秒";
        maxThinkingIntervalText.text = taskModel.taskMetrics.maxThinkingInterval.ToString("0.00") + "秒";
        averageSentenceLengthText.text = taskModel.taskMetrics.averageSentenceLength.ToString("0.00") + "字/句";
        eegValidTimeRatioText.text = taskModel.taskMetrics.eegValidTimeRatio * 100 + "%";
        // averageLLMResponseTimeText.text = taskModel.taskMetrics.averageLLMResponseTime.ToString("0.00") + "秒";
        // averageSTTResponseTimeText.text = taskModel.taskMetrics.averageSTTResponseTime.ToString("0.00") + "秒";
        // averageTTSResponseTimeText.text = taskModel.taskMetrics.averageTTSResponseTime.ToString("0.00") + "秒";
        nounCountText.text = taskModel.taskMetrics.nounCount + "个";
        verbCountText.text = taskModel.taskMetrics.verbCount + "个";
        verbNounRatioText.text = taskModel.taskMetrics.verbNounRatio * 100 + "%";
    }

    private void DisableStar() {
        for (var starsNum = 0; starsNum < 3; starsNum++) stars[starsNum].SetActiveFast(false);
    }

    private IEnumerator ShowStar() {
        //print("ShowStar");
        for (var starsNum = 0; starsNum < 3; starsNum++) {
            yield return new WaitForSeconds(0.5f);
            stars[starsNum].SetActiveFast(true);
        }
    }
}
