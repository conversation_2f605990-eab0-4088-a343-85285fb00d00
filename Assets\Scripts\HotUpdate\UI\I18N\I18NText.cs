using QFramework;
using UnityEngine;
using UnityEngine.UI;

// 不能重复添加这个组件
[DisallowMultipleComponent]
public class I18NText : <PERSON>o<PERSON><PERSON><PERSON>our, IController {
    public string key;
    private Text showText;

    public void Reset() {
        if (string.IsNullOrEmpty(key)) return;

        var s = this.GetSystem<II18NSystem>().GetText(key);
        if (showText != null && s.Length != 0) showText.text = s;
    }

    private void Start() {
        showText = GetComponent<Text>();
        this.GetSystem<II18NSystem>().RegisterText(this);
    }

    private void OnEnable() {
        if (this.GetSystem<II18NSystem>() != null)
            Reset();
    }

    private void OnDestroy() {
        if (this.GetSystem<II18NSystem>() != null)
            this.GetSystem<II18NSystem>().UnregisterText(this);
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }
}
