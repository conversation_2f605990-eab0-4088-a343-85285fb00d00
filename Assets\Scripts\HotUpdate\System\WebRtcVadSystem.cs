using System;
using QFramework;
using UnityEngine;

public interface IVadSystem : ISystem {
    public bool HasSpeech(byte[] audioData);
    public void VadInit();
    public void FreeVadInstance();
}

public class WebRtcVadSystem : AbstractSystem, IVadSystem {
    private bool init;
    private AndroidJavaObject mainActivity;
    private AndroidJavaObject VadWebRTC;

    /// <summary>
    ///     声音检测模式
    /// </summary>
    /// <summary>
    ///     初始化VAD
    /// </summary>
    public void VadInit() {
        if (Application.platform != RuntimePlatform.Android) return;
        init = true;
        // Initialize the SampleRate enum
        var sampleRateClass = new AndroidJavaClass("com.konovalov.vad.webrtc.config.SampleRate");
        var sampleRate16K = sampleRateClass.GetStatic<AndroidJavaObject>("SAMPLE_RATE_16K");

        // Initialize the FrameSize enum
        var frameSizeClass = new AndroidJavaClass("com.konovalov.vad.webrtc.config.FrameSize");
        var frameSize160 = frameSizeClass.GetStatic<AndroidJavaObject>("FRAME_SIZE_160");

        // Initialize the Mode enum
        var modeClass = new AndroidJavaClass("com.konovalov.vad.webrtc.config.Mode");
        var modeVeryAggressive =
            modeClass.GetStatic<AndroidJavaObject>(this.GetModel<IConfigModel>().Voice.VadMode.GetString());

        // Specify the other parameters
        var speechDurationMs = 0;
        var silenceDurationMs = 0;

        // Print the value to the console
        VadWebRTC = new AndroidJavaObject(
            "com.konovalov.vad.webrtc.VadWebRTC",
            sampleRate16K,
            frameSize160,
            modeVeryAggressive,
            speechDurationMs,
            silenceDurationMs
        );
    }


    /// <summary>
    /// </summary>
    /// <param name="audioData">包含需要进行VAD处理的音频数据的短整型数组。</param>
    /// <returns></returns>
    public bool HasSpeech(byte[] audioData) {
        if (!init) return false;
        var sbyteArray = Array.ConvertAll(audioData, b => unchecked((sbyte)b));
        return VadWebRTC.Call<bool>("isSpeech", sbyteArray);
    }

    public void FreeVadInstance() {
        if (Application.platform != RuntimePlatform.Android) return;
        if (!init) return;
        VadWebRTC.Call("close");
    }

    protected override void OnInit() {
    }
}

public enum Mode {
    NORMAL = 0,
    LOW_BITRATE = 1,
    AGGRESSIVE = 2,
    VERY_AGGRESSIVE = 3
}

public static class ModeExtensions {
    public static string GetString(this Mode mode) {
        switch (mode) {
            case Mode.NORMAL: return "NORMAL";
            case Mode.LOW_BITRATE: return "LOW_BITRATE";
            case Mode.AGGRESSIVE: return "AGGRESSIVE";
            case Mode.VERY_AGGRESSIVE: return "VERY_AGGRESSIVE";
            default: return null;
        }
    }
}
