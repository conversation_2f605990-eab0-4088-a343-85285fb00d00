using QFramework;
using UnityEngine;

public interface IIndexCharacterSystem : ISystem {
    public GameObject Character { get; set; }
    public MCharacterStyle MMCharacterStyle { get; set; }
}

public class IndexCharacterSystem : AbstractSystem, IIndexCharacterSystem {
    public GameObject Character { get; set; }
    public MCharacterStyle MMCharacterStyle { get; set; }

    protected override void OnInit() {
    }
}
