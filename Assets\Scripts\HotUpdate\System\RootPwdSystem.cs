using QFramework;

public interface IRootPwdSystem : ISystem {
    public void OpenRoot();
}

public class RootPwdSystem : AbstractSystem, IRootPwdSystem {
    public void OpenRoot() {
        if (this.GetModel<IRootModel>().RootPwdInput == this.GetModel<IConfigModel>().RootPwd) {
            this.SendEvent<RootOpenSuccEvent>();
        } else {
            var info = new WarningAlertInfo("密码错误！");
            this.SendEvent(new ShowPageEvent(UIPageType.WarningAlert, UILevelType.Alart, info));
            this.SendEvent<RootOpenFailEvent>();
        }
    }

    protected override void OnInit() {
    }
}
