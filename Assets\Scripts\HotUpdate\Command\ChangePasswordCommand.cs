﻿using QFramework;
using Utils;

public class ChangePasswordCommand : AbstractCommand {
    private readonly string mPassword;

    public ChangePasswordCommand(string password) {
        mPassword = password;
    }

    protected override async void OnExecute() {
        var requsetData = new UpdateUserPasswordRequest { password = Util.GetSha256(mPassword) };

        var request = await this.GetSystem<INetworkSystem>().PostJson<object>(
            RequestUrl.modifyPersonalInfoUrl,
            requsetData,
            this.GetModel<IUserModel>().Token.Value);

        if (request.IsSuccess) {
            var alertInfo = new WarningAlertInfo("修改成功");
            this.SendEvent(new ShowPageEvent(UIPageType.WarningAlert, UILevelType.Alart, alertInfo));
            this.GetModel<IUserModel>().Password.Value = mPassword;
        } else {
            var code = (int)request.StatusCode;
            if (code == 423) {
                var alertInfo = new WarningAlertInfo("修改失败");
                this.SendEvent(new ShowPageEvent(UIPageType.WarningAlert, UILevelType.Alart, alertInfo));
            } else if (code == 404) {
                var alertInfo = new WarningAlertInfo("信息错误");
                this.SendEvent(new ShowPageEvent(UIPageType.WarningAlert, UILevelType.Alart, alertInfo));
            }
        }
    }
}
