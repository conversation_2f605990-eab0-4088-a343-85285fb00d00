﻿using System.Collections;
using System.IO;
using UnityEngine;
using UnityEngine.UI;

public class ShareBtn : MonoBehaviour {
    private Button selfBtn;

    private void Start() {
        selfBtn = GetComponent<Button>();
        selfBtn.onClick.AddListener(() => { StartCoroutine(TakeScreenshotAndShare()); });
    }

    private IEnumerator TakeScreenshotAndShare() {
        yield return new WaitForEndOfFrame();

        var ss = new Texture2D(Screen.width, Screen.height, TextureFormat.RGB24, false);
        ss.ReadPixels(new Rect(0, 0, Screen.width, Screen.height), 0, 0);
        ss.Apply();

        var filePath = Path.Combine(Application.temporaryCachePath, "shared img.png");
        File.WriteAllBytes(filePath, ss.EncodeToPNG());

        // To avoid memory leaks
        Destroy(ss);

        new NativeShare().AddFile(filePath)
            .SetSubject("Subject goes here")
            .SetCallback((result, shareTarget) =>
                Debug.Log("Share result: " + result + ", selected app: " + shareTarget))
            .Share();

        // Share on WhatsApp only, if installed (Android only)
        //if( NativeShare.TargetExists( "com.whatsapp" ) )
        //	new NativeShare().AddFile( filePath ).AddTarget( "com.whatsapp" ).Share();
    }
}
