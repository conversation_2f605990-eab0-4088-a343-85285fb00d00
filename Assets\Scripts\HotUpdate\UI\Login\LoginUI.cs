﻿using System.Collections;
using QFramework;
using UnityEngine;
using UnityEngine.UI;

public class LoginUI : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IController {
    public InputField userNameInput;
    public InputField passwordInput;
    public Toggle rememberToggle;
    public Button loginBtn;
    public Button switchServerBtn;

    private void Awake() {
        print("LoginUI Awake");
    }

    private void Start() {
        print("LoginUI Start");
        rememberToggle.isOn = this.GetModel<IUserModel>().RememberPassword.Value == 1;
        if (this.GetModel<IUserModel>().RememberPassword.Value == 1) {
            userNameInput.text = this.GetModel<IUserModel>().UserName.Value;
            passwordInput.text = this.GetModel<IUserModel>().Password.Value;
        }

        loginBtn.onClick.AddListener(() => {
            print("clickBtn login");
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);

            if (userNameInput.text == "" || passwordInput.text == "") {
                var alertInfo = new WarningAlertInfo("Please enter the content");
                this.SendCommand(new ShowPageCommand(UIPageType.WarningAlert, UILevelType.Alart, alertInfo));
                StartCoroutine(LoginCoroutine());
                return;
            }

            loginBtn.interactable = false;
            this.SendCommand(new LoginCommand(userNameInput.text, passwordInput.text, rememberToggle.isOn));
            StartCoroutine(LoginCoroutine());
        });

        switchServerBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            this.SendCommand(new ShowPageCommand(UIPageType.SwitchServerUI));
        });
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    private IEnumerator LoginCoroutine() {
        yield return new WaitForSeconds(2f);
        loginBtn.interactable = true;
    }
}
