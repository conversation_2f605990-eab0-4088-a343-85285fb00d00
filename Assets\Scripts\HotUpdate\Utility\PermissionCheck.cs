﻿using System.Collections;
using UnityEngine;
using UnityEngine.Android;
using UnityEngine.UI;

public class PermissionCheck : MonoBehaviour {
    public GameObject m_Tip;
    public Text text;

    private bool isInternetCanUse;

    public void Start() {
        // 检查位置权限
        if (!Permission.HasUserAuthorizedPermission(Permission.FineLocation))
            // 请求位置权限
            Permission.RequestUserPermission(Permission.FineLocation);

        if (!Permission.HasUserAuthorizedPermission(Permission.CoarseLocation))
            // 请求位置权限
            Permission.RequestUserPermission(Permission.CoarseLocation);

        // 检查麦克风权限
        if (!Permission.HasUserAuthorizedPermission(Permission.Microphone))
            // 请求麦克风权限
            Permission.RequestUserPermission(Permission.Microphone);

        // 检查摄像头权限
        if (!Permission.HasUserAuthorizedPermission(Permission.Camera))
            // 请求摄像头权限
            Permission.RequestUserPermission(Permission.Camera);

        // 检查网络权限
        StartCoroutine(CheckInternetConnectionRoutine());
    }


    private void OnApplicationFocus(bool hasFocus) {
        if (hasFocus) CheckPermission();
    }

    private IEnumerator CheckInternetConnectionRoutine() {
        //while (true)
        //{
        var isInternetConnect = CheckInternetConnection();
        if (isInternetConnect)
            isInternetCanUse = true;
        //UnityWebRequest request = new("https://www.baidu.com")
        //{
        //    method = UnityWebRequest.kHttpVerbHEAD
        //};
        //yield return request.SendWebRequest();
        //if (request.result == UnityWebRequest.Result.Success)
        //{
        //    Debug.Log("网络连接可用");
        //    isInternetCanUse = true;
        //}
        //else
        //{
        //    Debug.Log("网络连接不可用");
        //    isInternetCanUse = false;
        //}
        else
            isInternetCanUse = false;
        yield break;
        //yield return new WaitForSeconds(10); // 每5秒检查一次网络连接
        //}
    }

    private bool CheckInternetConnection() {
        if (Application.internetReachability == NetworkReachability.NotReachable) {
            Debug.Log("没有网络连接");
            return false;
        }

        if (Application.internetReachability == NetworkReachability.ReachableViaCarrierDataNetwork) {
            Debug.Log("通过移动数据网络连接");
            return true;
        }

        if (Application.internetReachability == NetworkReachability.ReachableViaLocalAreaNetwork) {
            Debug.Log("通过WiFi连接");
            return true;
        }

        Debug.LogError("网络异常");
        return false;
    }

    public void OnClickTip() {
        CheckPermission();
    }

    private void CheckPermission() {
        // 检查用户是否授予了所有必要的权限
        if (Permission.HasUserAuthorizedPermission(Permission.FineLocation) &&
            Permission.HasUserAuthorizedPermission(Permission.CoarseLocation) &&
            Permission.HasUserAuthorizedPermission(Permission.Microphone) &&
            Permission.HasUserAuthorizedPermission(Permission.Camera) &&
            isInternetCanUse) {
            // 用户已经授予所有权限
            Debug.Log("所有权限已授予");
            m_Tip.SetActive(false);
        } else {
            // 用户没有授予所有权限
            Debug.Log("缺少必要权限");
            print("精确位置" + Permission.HasUserAuthorizedPermission(Permission.FineLocation));
            print("粗略位置" + Permission.HasUserAuthorizedPermission(Permission.CoarseLocation));
            print("麦克风" + Permission.HasUserAuthorizedPermission(Permission.Microphone));
            print("摄像头" + Permission.HasUserAuthorizedPermission(Permission.Camera));
            print("网络" + isInternetCanUse);

            if (!Permission.HasUserAuthorizedPermission(Permission.FineLocation)) text.text = "缺少精确位置权限";

            if (!Permission.HasUserAuthorizedPermission(Permission.CoarseLocation)) text.text = "缺少位置权限";

            if (!Permission.HasUserAuthorizedPermission(Permission.Microphone)) text.text = "缺少麦克风权限";

            if (!Permission.HasUserAuthorizedPermission(Permission.Camera)) text.text = "缺少摄像头权限";

            if (!isInternetCanUse) text.text = "缺少网络权限";

            m_Tip.SetActive(true);
        }
    }
}
