﻿using System.Collections.Generic;
using System.IO;
using QFramework;
using UnityEngine;
using UnityEngine.UI;
using Utils;

public class HistoryFile : MonoBeh<PERSON>our, IController {
    public TreeViewControl TreeView;
    public Button closeBtn;
    public Button shareBtn;

    private void Start() {
        closeBtn.onClick.AddListener(OnClickCloseBtn);
        shareBtn.onClick.AddListener(OnClickShareBtn);
    }


    private void Update() {
        ////判断树形菜单中名为“ 第一章 ”的元素是否被勾选
        //if (Input.GetKeyDown(KeyCode.A))
        //{
        //    bool isCheck = TreeView.ItemIsCheck("第一章");
        //    Debug.Log("当前树形菜单中的元素 第一章 " + (isCheck?"已被选中！":"未被选中！"));
        //}
    }

    public void OnEnable() {
        var generator = new TreeViewGenerator();
        var rootPath = Util.ArchiveInfoDir;
        var datas = generator.GenerateTreeViewDataFromDirectory(rootPath);
        //指定数据源
        TreeView.Data = datas;
        //重新生成树形菜单
        TreeView.GenerateTreeView();
        //刷新树形菜单
        TreeView.RefreshTreeView();
        //注册子元素的鼠标点击事件
        TreeView.ClickItemEvent += CallBack;
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    public void OnClickShareBtn() {
        this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
        var items = TreeView.ItemsIsCheck();
        for (var i = 0; i < items.Count; i++) Debug.Log("当前树形菜单中被选中的元素有：" + items[i]);
        var ZipDir = Path.Combine(Application.persistentDataPath, "Zip");
        Directory.CreateDirectory(ZipDir);
        DelOldFile(ZipDir);

        var outputPathName = Path.Combine(ZipDir, Util.NowTime() + ".zip");
        var fileOrDirectoryArray = items.ToArray();
        // 创建ZipFile实例，并指定压缩文件的名称  
        var flag = ZipUtility.Zip(fileOrDirectoryArray, outputPathName);
        print(outputPathName + "压缩 " + flag);

        // 用户选择了文件，可以在这里处理文件  
        Debug.Log("Selected file: " + outputPathName);
        new NativeShare().AddFile(outputPathName)
            .SetSubject("Subject goes here").SetText("Hello world!")
            .SetCallback((result, shareTarget) =>
                Debug.Log("Share result: " + result + ", selected app: " + shareTarget))
            .Share();
    }

    private void DelOldFile(string path) {
        // 获取目录下的所有文件
        var files = Directory.GetFiles(path);
        foreach (var file in files)
            // 删除每个文件
            File.Delete(file);
    }

    private void CallBack(GameObject item) {
        Debug.Log("点击了 " + item.transform.Find("TreeViewText").GetComponent<Text>().text);
        print(item.transform.Find("TreeViewText").GetComponent<ExtraNature>().path);
    }

    public void OnClickCloseBtn() {
        this.GetSystem<ISoundSystem>().PlaySound(SoundType.Close);
        this.SendCommand(new HidePageCommand(UIPageType.AllHistoryUI));
    }
}


public class TreeViewGenerator {
    private int nodeId;

    public List<TreeViewData> GenerateTreeViewDataFromDirectory(string rootPath) {
        var treeViewDataList = new List<TreeViewData>();
        var nodeIdDict = new Dictionary<string, int>(); // 用于存储路径到ID的映射  

        // 递归函数来遍历文件夹  
        void TraverseDirectory(string path, int parentId) {
            var directoryInfo = new DirectoryInfo(path);
            var node = new TreeViewData {
                ID = nodeId++,
                Name = directoryInfo.Name,
                Path = directoryInfo.FullName,
                ParentID = parentId
            };

            treeViewDataList.Add(node);
            nodeIdDict[path] = node.ID; // 存储路径到ID的映射，以便后续为子文件夹设置ParentID  

            // // 遍历子文件夹 
            foreach (var dir in directoryInfo.GetDirectories()) TraverseDirectory(dir.FullName, node.ID);

            // 遍历文件夹中的文件
            foreach (var file in directoryInfo.GetFiles()) {
                var fileNode = new TreeViewData {
                    ID = nodeId++,
                    Name = file.Name, // 可以选择包含扩展名或仅文件名  
                    Path = file.FullName, // 可以选择包含扩展名或仅文件名  
                    ParentID = node.ID, // 文件节点的ParentID是当前文件夹的ID  
                    IsFile = true
                };

                treeViewDataList.Add(fileNode);
                // 注意：这里不需要为文件存储路径到ID的映射，因为文件不是其他节点的父节点  
            }
        }

        TraverseDirectory(rootPath, -1); // 从根目录开始遍历  

        return treeViewDataList;
    }
}
