using System;
using System.Collections.Generic;
using QFramework;

[Serializable]
public class CharacterInfo {
    public string cid; // Prefab名
    public string name; // 暂时与cid一致
    public string gender; // 模型性别
}


public interface ICharacterModel : IModel {
    public Dictionary<string, CharacterInfo> CharacterDic { get; set; }
}

public class CharacterModel : AbstractModel, ICharacterModel {
    public Dictionary<string, CharacterInfo> CharacterDic { get; set; } = new();

    protected override void OnInit() {
    }
}
