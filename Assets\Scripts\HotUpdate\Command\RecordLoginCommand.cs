using Cysharp.Threading.Tasks;
using QFramework;
using UnityEngine;

public class RecordLoginCommand : AbstractCommand {
    protected override async void OnExecute() {
        // CoroutineController.manager.StartCoroutine(Util.GetPlace((place) => {
        //     RecordLogin(place);
        // }));
        await RecordLogin();
    }

    private async UniTask RecordLogin(string place = "Unknown") {
        Debug.Log("RecordLogin");

        var requsetData = new UserLoginRecordRequest {
            device = SystemInfo.deviceModel,
            place = place
        };

        var request = await this.GetSystem<INetworkSystem>().PostJson<ConfigResponse>(
            RequestUrl.recordLogin,
            requsetData,
            this.GetModel<IUserModel>().Token.Value);

        if (request.IsSuccess) {
            this.GetSystem<IDataParseSystem>().ParseConfigData(request.Data);
            CallBack();
        } else {
            if (request.StatusCode is not (401 or 204 or 203)) {
                Debug.LogError(RequestUrl.recordLogin + ": " + request.StatusCode +
                               " " + request.Data + " " + request.ErrorMessage);
                var info = new InfoConfirmInfo("", "啊哦~服务器出错！\n请联系管理员或重启APP！\n点击确定后，将退出APP。",
                    Application.Quit
                    , type: ConfirmAlertType.Single);
                this.SendEvent(new ShowPageEvent(UIPageType.InfoConfirmAlert, UILevelType.Alart, info));
            }
        }
    }

    private void CallBack() {
        this.GetSystem<IVadSystem>().VadInit();
    }
}
