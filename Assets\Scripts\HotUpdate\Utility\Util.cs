﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using Cysharp.Threading.Tasks;
using JiebaNet.Segmenter.PosSeg;
using LitJson;
using Newtonsoft.Json;
using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.OpenSsl;
using Org.BouncyCastle.Security;
using UnityEngine;
using UnityEngine.Networking;
using UnityEngine.UI;
using Object = UnityEngine.Object;

namespace Utils {
    public enum SceneID {
        Login = 0, // 登录
        Index = 1, // 主页面
        Loading = 2, // 加载页面
        Task = 3 // 任务界面 各个任务范式穿插其中
    }

    public static class GameObjectExtension {
        public static void SetActiveFast(this GameObject o, bool s) {
            if (o.activeSelf != s) o.SetActive(s);
        }
    }

    public static class Json {
        public static void JsonDump(string path, object value) {
            // 格式化保存
            var settings = new JsonSerializerSettings {
                NullValueHandling = NullValueHandling.Ignore,
                DefaultValueHandling = DefaultValueHandling.Ignore
            };

            var json = JsonConvert.SerializeObject(value, Formatting.Indented, settings);
            File.WriteAllText(path, json);
        }

        public static T ReadJsonFromFile<T>(string filePath) {
            try {
                var json = File.ReadAllText(filePath);
                return JsonConvert.DeserializeObject<T>(json);
            } catch (Exception ex) {
                Console.WriteLine("Error reading JSON file: " + ex.Message);
                return default;
            }
        }
    }

    public static class Util {
        public static string baseLanguagePath = "Assets/AA/TextFiles/Language/";
        public static string baseFlagPath = "Assets/AA/Sprites/LanguageFlags/";
        public static string jiebaDictLabel = "jiebaDict";

        public static void AppQuit() {
            if  (Application.platform == RuntimePlatform.WindowsEditor) {
                // 编辑器退出
                #if UNITY_EDITOR
                UnityEditor.EditorApplication.isPlaying = false;
                #endif
            } else {
                Application.Quit();
            }
        }

        public static string GetContentFromHexString(string hex) {
            var buf1 = new byte[hex.Length / 2];
            for (var i = 0; i < hex.Length; i += 2)
                // 将 16进制字符串 中的每两个字符转换成 byte，并加入到新申请的 byte数组 中
                buf1[i / 2] = Convert.ToByte(hex.Substring(i, 2), 16);
            return Encoding.UTF8.GetString(buf1);
        }

        public static DateTime GetUTCDateTime(long timeStamp) {
            var dateTimeStart = new DateTime(1970, 1, 1);
            var time = dateTimeStart.AddSeconds(timeStamp);
            return time;
        }

        public static Coroutine DelayExecute(Action f, Func<bool> pre) {
            return CoroutineController.manager.StartCoroutine(DelayExecuting(f, pre));
        }

        private static IEnumerator DelayExecuting(Action f, Func<bool> pre) {
            // at least wait for two frame
            yield return null;
            yield return null;
            yield return new WaitUntil(pre);
            f();
        }

        public static void DelayExecuteWithSecond(float sec, Action func) {
            CoroutineController.manager.StartCoroutine(DelayExecutingWithSecond(sec, func));
        }

        private static IEnumerator DelayExecutingWithSecond(float sec, Action func) {
            yield return new WaitForSeconds(sec);
            func?.Invoke();
        }

        public static void DeleteChildren(Transform t, int startIndex = 0) {
            var children = new List<Transform>();
            for (var i = startIndex; i < t.childCount; i++) children.Add(t.GetChild(i));

            foreach (var child in children) {
                child.SetParent(null);
                Object.Destroy(child.gameObject);
            }
        }

        public static string SecondToStirngWithFormat(double sec, string format) {
            format = format.ToLower();
            var availableList = new List<string> { "h:m:s:c", "m:s:c", "h:m:s", "m:s", "h:m:s.c", "s" };
            if (!availableList.Contains(format)) return "Time with Error Format, Add To Config First";

            if (sec < 0) sec = 0;

            var ts = new TimeSpan((long)(sec * 10000000));
            var num_left3 = (int)(1000 * sec);
            var num_int = (int)sec;
            var num = num_left3 - num_int * 1000;
            var str = "";
            switch (format) {
                case "h:m:s.c":
                    str = ts.Hours.ToString("00") + ":" + ts.Minutes.ToString("00") + ":" + ts.Seconds.ToString("00") +
                          "." + (num / 10).ToString("00");
                    break;
                case "h:m:s:c":
                    str = ts.Hours.ToString("00") + ":" + ts.Minutes.ToString("00") + ":" + ts.Seconds.ToString("00") +
                          ":" + (num / 10).ToString("00");
                    break;
                case "m:s:c":
                    str = (ts.Hours * 60 + ts.Minutes).ToString("00") + ":" + ts.Seconds.ToString("00") + "." +
                          (num / 10).ToString("00");
                    break;
                case "h:m:s":
                    str = ts.Hours.ToString("00") + ":" + ts.Minutes.ToString("00") + ":" + ts.Seconds.ToString("00");
                    break;
                case "m:s":
                    str = (ts.Hours * 60 + ts.Minutes).ToString("00") + ":" + ts.Seconds.ToString("00");
                    break;
                case "s":
                    str = ts.Seconds.ToString("00");
                    break;
            }

            return str;
        }

        public static string GetDateTime(long timeStamp, string format) {
            var dtStart = TimeZoneInfo.ConvertTimeToUtc(new DateTime(1970, 1, 1));
            var lTime = timeStamp * 10000000;
            var toNow = new TimeSpan(lTime);
            var targetDt = dtStart.Add(toNow);
            return targetDt.ToString(format);
        }

        public static string GetPlatform() {
#if UNITY_STANDALONE
            return "PC";
#elif UNITY_ANDROID
            return "Android";
#elif UNITY_IOS
            return "ios";
#elif UNITY_WEBGL
            return "WebGL";
#endif
        }

        public static List<string> GetFileDirectory(string path) {
            var list = new List<string>();
            var root = new DirectoryInfo(path);
            var files = root.GetFiles();
            for (var i = 0; i < files.Length; i++) list.Add(files[i].Name);

            return list;
        }

        public static Vector2 WorldToCanvasPos(Canvas canvas, Vector3 world) {
            Vector2 position;
            RectTransformUtility.ScreenPointToLocalPointInRectangle(canvas.transform as RectTransform, world,
                canvas.GetComponent<Camera>(), out position);
            return position;
        }

        // TODO 这个方法有问题，可能会导致主线程卡顿
        public static IEnumerator GetPlace(Action<string> finishAction) {
            using (var request =
                   new UnityWebRequest("http://ip-api.com/json/?lang=zh-CN", UnityWebRequest.kHttpVerbPOST)) {
                request.downloadHandler = new DownloadHandlerBuffer();
                request.SetRequestHeader("Content-Type", "application/json");
                request.SetRequestHeader("Accept", "application/json");
                yield return request.SendWebRequest();

                if (request.result == UnityWebRequest.Result.ConnectionError ||
                    request.result == UnityWebRequest.Result.ProtocolError) {
                    finishAction.Invoke("Unknown");
                } else {
                    var results = request.downloadHandler.data;
                    var s = Encoding.UTF8.GetString(results);
                    var d = JsonMapper.ToObject(s);
                    string regionName;
                    try {
                        regionName = (string)d["regionName"];
                    } catch {
                        regionName = "";
                    }

                    string city;
                    try {
                        city = (string)d["city"];
                    } catch {
                        city = "";
                    }

                    finishAction.Invoke(regionName + "," + city);
                }
            }
        }

        public static string GetSha256(string strToEncrypt) {
            // 使用UTF8编码将字符串转换为字节数组
            var ue = new UTF8Encoding();
            var bytes = ue.GetBytes(strToEncrypt);

            // 创建一个SHA256哈希算法提供者
            using (var sha256Hash = SHA256.Create()) {
                // 计算字节数组的哈希值
                var hashBytes = sha256Hash.ComputeHash(bytes);

                // 将哈希字节数组转换为16进制字符串
                var builder = new StringBuilder();
                for (var i = 0; i < hashBytes.Length; i++) builder.Append(hashBytes[i].ToString("x2"));
                return builder.ToString();
            }
        }

        public static string GetAvatarUrl(int avatarId) {
            return $"Assets/AA/Sprites/Avatar/{avatarId}.png";
        }

        public static string GetCharacterUrl(string cid) {
            return $"Assets/AA/Character/{cid}/{cid}.prefab";
        }

        public static string GetCharacterSpriteUrl(string cid) {
            return $"Assets/AA/Character/Image/{cid}.png";
        }

        public static string GetCharacterAnimatorUrl(string gender) {
            return $"Assets/AA/Animations/KEKOS/{gender}.controller";
        }

        // TODO 要不要输出查到的敏感词，然后返回给AI
        public static bool CheckeMsgIsLegal(string msg) {
            if (msg == null)
                // 处理 msg 为 null 的情况，例如返回 false 或抛出异常
                return false;
            var illegalList = new HashSet<string>();
            illegalList.Add("傻逼");
            illegalList.Add("傻逼2");
            illegalList.Add("傻逼3");
            foreach (var illegalText in illegalList)
                if (msg.Contains(illegalText))
                    return false;

            return true;
        }

        /// <summary>
        /// 音频系统测试工具
        /// </summary>
        public static void TestAudioSystem()
        {
            Debug.Log("=== 开始音频系统测试 ===");

            var audioManager = Atis.Interface.GetSystem<IAudioManagerSystem>();
            if (audioManager == null)
            {
                Debug.LogError("音频管理器未初始化");
                return;
            }

            // 输出当前状态
            Debug.Log($"缓冲区状态: {audioManager.GetBufferStatus()}");
            Debug.Log($"自适应策略状态: {audioManager.GetAdaptiveBufferStatus()}");

            // 输出性能报告
            var report = AudioPerformanceTester.GeneratePerformanceReport(audioManager);
            Debug.Log(report);

            Debug.Log("=== 音频系统测试完成 ===");
        }

        /// <summary>
        ///     将一个可能不合法的文件名转换为合法的文件名
        /// </summary>
        /// <param name="name"></param>
        /// <param name="defaultName"></param>
        /// <returns></returns>
        public static string ToValidFileName(string name) {
            // 替换不允许的字符
            var invalidChars = Regex.Escape(new string(Path.GetInvalidFileNameChars()));
            var invalidRegStr = $"[{invalidChars}]+";
            name = Regex.Replace(name, invalidRegStr, "_");

            // 替换空格（可选）
            name = name.Replace(" ", "_");

            // 确保文件名不以点开头
            if (name.StartsWith(".")) name = "_" + name.TrimStart('.');

            // 确保文件名不为空
            if (string.IsNullOrWhiteSpace(name)) name = "invalidFileName_" + NowTime();

            return name;
        }

        /// <summary>
        ///     统计文本中的名词和动词数量
        ///     输出结果
        ///     Debug.Log($"总词数: {result.Item3}");
        ///     Debug.Log($"名词个数: {result.Item1}");
        ///     Debug.Log($"动词个数: {result.Item2}");
        /// </summary>
        /// <param name="text"></param>
        /// <returns></returns>
        public static Tuple<int, int, int> CountNounsAndVerbs(string text) {
            var nounCount = 0;
            var verbCount = 0;
            var totalWordCount = 0;

            // 使用词性标注分词器
            var tokens = new PosSegmenter().Cut(text);

            foreach (var token in tokens) {
                // 统计总词数
                totalWordCount++;

                // 统计名词和动词
                if (token.Flag.StartsWith("n")) // 名词
                    nounCount++;
                else if (token.Flag.StartsWith("v")) // 动词
                    verbCount++;
            }

            return Tuple.Create(nounCount, verbCount, totalWordCount);
        }

        public static void RenameFile(string originalFilePath, string newFilePath) {
            // 检查原始文件是否存在
            if (File.Exists(originalFilePath))
                // 重命名文件
                File.Move(originalFilePath, newFilePath);
            // Debug.Log("文件已重命名成功！");
            else
                Debug.LogError("原始文件不存在！");
        }

        #region 原来的

        public static string ArchiveInfoDir;

        public const string Pardon2AudioPath = "Assets/AA/Audio/ChatAudio/pardon2.mp3";
        public const string Pardon2AudioText = "不好意思，我刚开小差了，请再说一遍";
        public const string NoNetPath = "Assets/AA/Audio/ChatAudio/NoNet.mp3";
        public const string NoNetText = "我找不到网络信号了...\n可能是WiFi偷懒啦！\n点击确定后，将退出APP";
        public const string ServerErrorPath = "Assets/AA/Audio/ChatAudio/ServerError.mp3";
        public const string ServerErrorText = "服务端出错啦...\n请联系管理员！\n点击确定后，将退出APP";

        private static string _userChatDataDir;
        public static string DateDir;
        public static string ThemeDir;
        public static string LogDir; // GameHelper 日志保存路径

        public const string CharactersJsonPath = "Assets/AA/Character/characters.json";
        public const string AvatarJsonPath = "Assets/AA/Sprites/Avatar/avatar.json";
        public const string HotFixLogJsonPath = "Assets/AA/TextFiles/HotFixLog/HotFixLog.json";

        public const string BasePageUrl = "Assets/AA/Prefabs/UIPage/";
        public const string PageSuffix = ".prefab";

        public const string BalloonRootPath = "Assets/AA/Sprites/Icons/Icons Colored/Balloons/";
        public const string BalloonAnimUrl = "Assets/AA/Prefabs/Utility/balloonAnim.prefab";

        public static void InitPath() {
            LogDir = Path.Combine(Application.persistentDataPath, "Log");
            if (!Directory.Exists(LogDir)) Directory.CreateDirectory(LogDir);

            ArchiveInfoDir = Path.Combine(Application.persistentDataPath, "Archive");

            Directory.CreateDirectory(ArchiveInfoDir);
        }

        public static void SetUserChatDataDir(string uFileName) {
            _userChatDataDir = Path.Combine(ArchiveInfoDir, ToValidFileName(uFileName));
            PlayerPrefs.SetString("userChatDataDir", _userChatDataDir);
            DateDir = Path.Combine(PlayerPrefs.GetString("userChatDataDir"), DateTime.Now.ToString("yyyy-MM-dd"));
            Directory.CreateDirectory(DateDir);
        }

        public static void SetThemeDir(string taskName) {
            DateDir = Path.Combine(PlayerPrefs.GetString("userChatDataDir"), DateTime.Now.ToString("yyyy-MM-dd"));
            ThemeDir = Path.Combine(DateDir, ToValidFileName(taskName));
            Directory.CreateDirectory(ThemeDir);
        }

        public static string NowTime() {
            return DateTime.Now.ToString("yyyyMMdd_HHmmss");
        }

        public static string NowDate() {
            return DateTime.Now.ToString("yyyyMMdd");
        }


        public static async UniTask<byte[]> ReadFileAsync(string path) {
            using var fs = new FileStream(path, FileMode.Open);
            var buffer = new byte[fs.Length];
            await fs.ReadAsync(buffer, 0, (int)fs.Length);
            return buffer;
        }

        public static void MoveFile(string source, string destination) {
            try {
                // 移动文件  用Move时，安卓端源视频文件并没有被删除，但是被损坏了，所以这里换一种方式尝试一下 TODO
                if (File.Exists(destination)) return;
                File.Copy(source, destination);
                File.Delete(source);
                Debug.Log("文件移动成功！");
            } catch (Exception ex) {
                // 处理异常，比如文件不存在，或者没有足够的权限等
                Debug.LogError("文件移动失败：" + ex.Message);
            }
        }

        #endregion

        # region Discarded Functions

        public static Dictionary<string, Sprite> cachedImageDic = new();

        public static IEnumerator DownloadImage(string url, Image img, Action func = null) {
            if (!url.StartsWith("http")) yield break;

            if (cachedImageDic.ContainsKey(url)) {
                if (img != null) img.sprite = cachedImageDic[url];
                func?.Invoke();
                yield break;
            }

            var request = UnityWebRequest.Get(url);
            request.SetRequestHeader("Version", Application.version);
            yield return request.SendWebRequest();
            if (request.result == UnityWebRequest.Result.ConnectionError) {
            } else if (request.isDone) {
                try {
                    var bytes = request.downloadHandler.data;
                    var texture = new Texture2D(1, 1, TextureFormat.RGBA32, false);
                    texture.LoadImage(bytes);
                    texture.wrapMode = TextureWrapMode.Clamp;
                    cachedImageDic[url] = Sprite.Create(texture, new Rect(0, 0, texture.width, texture.height),
                        new Vector2(0.5f, 0.5f));
                    if (img != null)
                        img.sprite = cachedImageDic[url];
                    if (func != null)
                        func();
                } catch (Exception e) {
                    Debug.Log(e);
                }
            }
        }

        public static IEnumerator DownloadImageToLocal(string url, string path, Image image, Action func = null) {
            if (!url.StartsWith("http")) yield break;

            if (cachedImageDic.ContainsKey(url)) {
                if (image != null) image.sprite = cachedImageDic[url];
                func?.Invoke();
                yield break;
            }

            var request = UnityWebRequest.Get(url);
            request.SetRequestHeader("Version", Application.version);
            yield return request.SendWebRequest();
            if (request.result == UnityWebRequest.Result.ConnectionError) {
                //Debug.LogError("+++++++ActivityUI 下载失败" + aid);
            } else if (request.isDone) {
                try {
                    var bytes = request.downloadHandler.data;
                    // 转Texture2D会被压缩
                    File.WriteAllBytes(path, bytes);
                    var texture = new Texture2D(1, 1, TextureFormat.RGBA32, true);
                    texture.LoadImage(bytes);
                    var pngData = texture.EncodeToPNG();
                    cachedImageDic[url] = Sprite.Create(texture, new Rect(0, 0, texture.width, texture.height),
                        new Vector2(0.5f, 0.5f));
                    if (image != null) image.sprite = cachedImageDic[url];
                } catch (Exception e) {
                    Debug.Log(e);
                }
            }
        }

        public static string Md5SumOfFile(string filepath) {
            try {
                byte[] hashBytes;
                using (var md5 = MD5.Create()) {
                    using (var stream = File.OpenRead(filepath)) {
                        hashBytes = md5.ComputeHash(stream);
                    }
                }

                // Convert the encrypted bytes back to a string (base 16)
                var hashString = "";

                for (var i = 0; i < hashBytes.Length; i++)
                    hashString += Convert.ToString(hashBytes[i], 16).PadLeft(2, '0');

                return hashString.PadLeft(32, '0');
            } catch (Exception e) {
                Debug.LogError(e);
                return "";
            }
        }

        public static string GetMd5(string strToEncrypt) {
            var ue = new UTF8Encoding();
            var bytes = ue.GetBytes(strToEncrypt);

            // encrypt bytes
            var md5 = new MD5CryptoServiceProvider();
            var hashBytes = md5.ComputeHash(bytes);

            // Convert the encrypted bytes back to a string (base 16)
            var hashString = "";

            for (var i = 0; i < hashBytes.Length; i++) hashString += Convert.ToString(hashBytes[i], 16).PadLeft(2, '0');

            return hashString.PadLeft(32, '0');
        }

        public static IEnumerator LoadStreamingFileOnAndroid(Action<string> finishAction) {
            var filePath = Path.Combine(Application.streamingAssetsPath, "gitignore", "private_key.pem");
            // 使用 UnityWebRequest 读取文件
            using (var request = UnityWebRequest.Get(filePath)) {
                yield return request.SendWebRequest();
                while (!request.isDone) yield return null;
                if (request.result == UnityWebRequest.Result.Success) {
                    var fileContent = request.downloadHandler.text;
                    // Debug.Log("File content: " + fileContent);
                    finishAction.Invoke(fileContent);
                } else {
                    Debug.LogError("Failed to load file: " + request.error);
                }
            }
        }

        public static string LoadStreamingFile() {
            var path = Path.Combine(Application.streamingAssetsPath, "gitignore", "private_key.pem");
            return File.ReadAllText(path);
        }

        public static string RSADecrypt(string content, string pemPrivateKey) {
            var rsa = new RSACryptoServiceProvider();
            byte[] cipherbytes;
            var xmlPrivateKey = ConvertPEMPrivateKeyToXML(pemPrivateKey);
            rsa.FromXmlString(xmlPrivateKey);
            cipherbytes = rsa.Decrypt(Convert.FromBase64String(content), false);
            return Encoding.UTF8.GetString(cipherbytes);
        }

        public static string ConvertPEMPrivateKeyToXML(string pemkey) {
            // 将PEM格式的密钥中的私钥标识替换为空
            pemkey = pemkey.Replace("-----BEGIN PRIVATE KEY-----", "").Replace("-----END PRIVATE KEY-----", "");
            // 声明变量
            var rsaKey = string.Empty;
            object pemObject = null;
            var rsaPara = new RSAParameters();

            // 从字符串读取PEM格式的密钥
            using (var sReader = new StringReader(pemkey)) {
                var pemReader = new PemReader(sReader);
                pemObject = pemReader.ReadObject();
            }

            // 获取RSA私钥参数
            var key = (RsaPrivateCrtKeyParameters)PrivateKeyFactory.CreateKey(Convert.FromBase64String(pemkey));
            rsaPara = new RSAParameters {
                Modulus = key.Modulus.ToByteArrayUnsigned(),
                Exponent = key.PublicExponent.ToByteArrayUnsigned(),
                D = key.Exponent.ToByteArrayUnsigned(),
                P = key.P.ToByteArrayUnsigned(),
                Q = key.Q.ToByteArrayUnsigned(),
                DP = key.DP.ToByteArrayUnsigned(),
                DQ = key.DQ.ToByteArrayUnsigned(),
                InverseQ = key.QInv.ToByteArrayUnsigned()
            };

            // 将RSA私钥参数导入到RSACryptoServiceProvider对象
            var rsa = new RSACryptoServiceProvider();
            rsa.ImportParameters(rsaPara);

            // 将RSACryptoServiceProvider对象转换为XML格式的字符串
            using (var sw = new StringWriter()) {
                sw.Write(rsa.ToXmlString(true));
                rsaKey = sw.ToString();
            }

            return rsaKey;
        }

        #endregion
    }
}
