﻿using Cysharp.Threading.Tasks;
using QFramework;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.ResourceManagement.AsyncOperations;
using UnityEngine.UI;
using Utils;

public class ChangeCharacterItem : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IPointer<PERSON><PERSON><PERSON><PERSON><PERSON>, IController {
    public Image bg;
    public Image showImage;
    public Image selectIamge;
    public CharacterInfo characterInfo;
    public Color normalColor;

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    public void OnPointerClick(PointerEventData eventData) {
        this.SendCommand(new ChangeCharacterCommand(characterInfo.cid));
    }

    public async UniTask SetContent(CharacterInfo info) {
        characterInfo = info;
        var obj = await this.GetSystem<IAddressableSystem>()
            .LoadAssetAsync<GameObject>(Util.GetCharacterUrl(characterInfo.cid));

        var obj1 = await this.GetSystem<IAddressableSystem>()
            .LoadAssetAsync<Sprite>(Util.GetCharacterSpriteUrl(characterInfo.cid));
        if (obj1.Status == AsyncOperationStatus.Succeeded)
            showImage.sprite = Instantiate(obj1.Result, transform, false);
    }

    public void SetSelectState(bool isSelect) {
        bg.color = isSelect ? Color.white : normalColor;
        selectIamge.gameObject.SetActiveFast(isSelect);
    }
}
