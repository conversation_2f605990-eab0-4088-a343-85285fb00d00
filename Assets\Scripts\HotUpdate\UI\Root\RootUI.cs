﻿using Builtin.Utility;
using QFramework;
using UnityEngine;
using UnityEngine.UI;
using Utils;

public class RootUI : MonoBehaviour, IController {
    public Button closeBtn;
    public Button logoutBtn;
    public Button historyBtn;
    public Button exitBtn;
    public Button audioVisualDemoBtn;
    public Toggle eegToggle;
    public Toggle eegCorrectToggle;
    public Button hotFixLogBtn;
    public Button switchServerBtn;
    public Button adjustSpeechSpeedBtn;
    public Button multiSelectPrefsManagerBtn;

    private void Awake() {
        multiSelectPrefsManagerBtn.gameObject.SetActive(this.GetSystem<INetworkSystem>().ServerType !=
                                                        ServerTypeAot.Release);
    }

    private void Start() {
        closeBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Close);
            gameObject.SetActiveFast(false);
        });
        logoutBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            gameObject.SetActiveFast(false);
            this.GetModel<IUserModel>().IsSuperuser.Value = false;
        });
        historyBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            this.SendCommand(new ShowPageCommand(UIPageType.AllHistoryUI));
        });

        exitBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);

            var info = new InfoConfirmInfo("退出账号", "即将退出账号，并退出程序",
                () => {
                    this.GetModel<IUserModel>().RememberPassword.Value = 0;
                    Util.AppQuit();
                },
                () => { this.SendCommand(new HidePageCommand(UIPageType.InfoConfirmAlert)); });
            this.SendCommand(new ShowPageCommand(UIPageType.InfoConfirmAlert, UILevelType.Alart, info));
        });

        eegToggle.onValueChanged.AddListener(e => {
            var eegModel = this.GetModel<IEegModel>();
            eegModel.isActive.Value = e;
            if (!e) eegModel.bluetoothManager.DisConnectBle(eegModel.bluetoothManager.GetBindDevice());
            if (e) this.GetModel<IEegModel>().bluetoothManager.Scan();
        });

        eegCorrectToggle.onValueChanged.AddListener(e => {
            var eegModel = this.GetModel<IEegModel>();
            eegModel.isCorrectedRoot.Value = e;
        });

        audioVisualDemoBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            this.SendCommand(new ShowPageCommand(UIPageType.AudioVisualDemo));
        });

        switchServerBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            this.SendCommand(new ShowPageCommand(UIPageType.SwitchServerUI));
        });

        eegToggle.isOn = this.GetModel<IEegModel>().isActive.Value;
        eegCorrectToggle.isOn = false;

        hotFixLogBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            this.SendCommand(new ShowPageCommand(UIPageType.HotFixLog));
        });

        adjustSpeechSpeedBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            this.SendCommand(new ShowPageCommand(UIPageType.AdjustSpeechSpeedUI));
        });

        multiSelectPrefsManagerBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            this.SendCommand(new ShowPageCommand(UIPageType.MultiSelectPrefsManagerUI));
        });
    }


    private void OnEnable() {
        // statusUI.gameObject.SetActiveFast(false);
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }
}
