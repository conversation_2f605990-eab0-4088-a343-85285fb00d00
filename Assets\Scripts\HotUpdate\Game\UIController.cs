﻿using System;
using System.Collections.Generic;
using System.Linq;
using QFramework;
using UnityEngine;
using UnityEngine.ResourceManagement.AsyncOperations;
using Utils;

public enum UIPageType {
    HomepageAtisUI = 0, // atis 主界面
    LoginAtisUI, // atis 登录
    ProfileUI, // 个人信息UI
    SettingsUI, // 设置页面UI
    AllHistoryUI, // 全部历史记录
    EEGBluetoothUI, // EEG 设备操作界面
    ChangeCharacterUI, // 修改和查看虚拟形象UI
    SwitchServerUI, // 选择服务端UI

    PauseUI, // 暂停UI
    QuitUI, // 退出UI
    TaskResultUI, // 每个任务结算UI
    QuestionnaireUI, // 问卷页面

    WarningAlert, // 警告弹窗
    InfoConfirmAlert, // 确认信息UI
    LoadingUI, // 加载

    GameHelper, // TODO： 开发者查看调试信息UI - 如何调用
    RootPwdUI, // 输入管理员密码UI
    RootUI, // 管理员界面UI （主要是连接蓝牙功能）
    AudioVisualDemo, // 声音可视化测试UI
    HotFixLog, // 热更新日志
    AdjustSpeechSpeedUI, // 调整语速
    MultiSelectPrefsManagerUI, // 清除Prefs 测试用

    ChatUI
}

public enum UILevelType {
    Prepare = 0, // LoadingUI
    Main, // 切换场景后，放置主要UI？？？
    UIPage, // UIPageType
    Popup,
    Alart, // 放置各种弹窗 InfoConfirmAlert WarningAlert LoadingUI
    Debug // 放置 GameHelper
}


public class UIController : MonoBehaviour, IController {
    public Transform[] levles;
    private readonly Dictionary<UIPageType, GameObject> pagesDict = new();
    private readonly Dictionary<UILevelType, LinkedList<UIPageType>> pagesGroup = new();

    private void Awake() {
        this.RegisterEvent<HidePageEvent>(OnHidePage).UnRegisterWhenGameObjectDestroyed(gameObject);
        this.RegisterEvent<ShowPageEvent>(OnShowPage).UnRegisterWhenGameObjectDestroyed(gameObject);
        this.RegisterEvent<HidePageByLevelEvent>(OnHidePageByLevel).UnRegisterWhenGameObjectDestroyed(gameObject);
        this.RegisterEvent<PrepareUIEvent>(OnPrepareUI).UnRegisterWhenGameObjectDestroyed(gameObject);

        DontDestroyOnLoad(gameObject);
    }

    private void OnDestroy() {
        foreach (var kv in pagesDict)
            if (kv.Value != null)
                Destroy(kv.Value);

        pagesDict.Clear();
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    private void OnPrepareUI(PrepareUIEvent obj) {
        this.GetSystem<IGuidanceSystem>().UIControllerCanvas = GetComponent<Canvas>();
        foreach (UILevelType value in Enum.GetValues(typeof(UILevelType)))
            pagesGroup[value] = new LinkedList<UIPageType>();
    }

    private void OnHidePage(HidePageEvent e) {
        if (!pagesDict.ContainsKey(e.pageType)) {
            Debug.Log("Not Exist Page " + e.pageType);
            return;
        }

        pagesDict[e.pageType].SetActiveFast(false);
    }

    // 根据对应页面类型显示页面 如果没有页面则创建 如果有页面则调取并active为true 第二个参数是是否关闭其他开启界面
    private void OnShowPage(ShowPageEvent e) {
        if (e.closeOther)
            foreach (var kv in pagesDict)
                kv.Value.SetActiveFast(false);

        if (pagesDict.ContainsKey(e.pageType) && pagesGroup[e.levelType].Contains(e.pageType)) {
            // 当前页面还在原来分组
            pagesDict[e.pageType].SetActiveFast(true);
            SetPageInfo(e);
        } else if (pagesDict.ContainsKey(e.pageType) && !pagesGroup[e.levelType].Contains(e.pageType)) {
            // 当前页面还在不在原来分组
            pagesDict[e.pageType].transform.SetParent(levles[(int)e.levelType], false);
            pagesGroup[GetGroupByPageType(e.pageType)].Remove(e.pageType);
            pagesGroup[e.levelType].AddLast(e.pageType);
            SetPageInfo(e);
        } else {
            var pageUrl = GetPageUrlByType(e.pageType);
            print("pageUrl:" + pageUrl);

            this.GetSystem<IAddressableSystem>().LoadAssetCb<GameObject>(pageUrl, obj => {
                if (obj.Status == AsyncOperationStatus.Succeeded) {
                    var page = Instantiate(obj.Result);
                    page.transform.SetParent(levles[(int)e.levelType], false);
                    pagesDict[e.pageType] = page;
                    pagesGroup[e.levelType].AddLast(e.pageType);
                    SetPageInfo(e);
                } else {
                    Debug.LogError($"Load {e.pageType} Page Failed");
                }
            }).Forget();
        }
    }

    private void SetPageInfo(ShowPageEvent e) {
        var penal = pagesDict[e.pageType].GetComponent<UIPenal>();
        if (e.data != null && penal != null) penal.InitData(e.data);

        pagesDict[e.pageType].transform.SetAsLastSibling();
    }

    private UILevelType GetGroupByPageType(UIPageType type) {
        foreach (var kv in pagesGroup)
            if (kv.Value.Contains(type))
                return kv.Key;

        return UILevelType.Main;
    }

    private void OnHidePageByLevel(HidePageByLevelEvent e) {
        foreach (var kv in pagesGroup[e.mLevelType])
            if (pagesDict.ContainsKey(kv))
                pagesDict[kv].SetActiveFast(false);
    }

    private void DestoryPageByLevel(UILevelType levelType) {
        foreach (var kv in pagesGroup[levelType])
            if (pagesDict.ContainsKey(kv)) {
                Destroy(pagesDict[kv]);
                pagesDict.Remove(kv);
                pagesGroup[levelType].Remove(kv);
            }
    }

    // 获取对应页面
    private GameObject GetPage(UIPageType type) {
        if (pagesDict.ContainsKey(type)) return pagesDict[type];

        return null;
    }

    //销毁对应页面
    private void DestoryPage(UIPageType type) {
        if (!pagesDict.ContainsKey(type)) {
            Debug.LogError("not exist page");
            return;
        }

        Destroy(pagesDict[type]);
        pagesDict.Remove(type);
    }

    // 判断当前页面是否是主页
    private bool IsHomePage() {
        var isHomePage = true;
        pagesDict.ToList().ForEach(delegate(KeyValuePair<UIPageType, GameObject> pair) {
            if (pair.Value != null && pair.Key != UIPageType.HomepageAtisUI && pair.Value.activeSelf)
                isHomePage = false;
        });
        return isHomePage && pagesDict[UIPageType.HomepageAtisUI] != null;
    }

    // 查找Resources中的路径
    private string GetPageUrlByType(UIPageType type) {
        return Util.BasePageUrl + type + Util.PageSuffix;
    }
}
