using System.Collections;
using System.Threading.Tasks;
using QFramework;
using UnityEngine.SceneManagement;
using Utils;

public interface IChatSystem : ISystem {
    public IEnumerator WaitResume();
    public Task WaitResumeAsync();
    // public void ChangeToInputState();
}

public class ChatSystem : AbstractSystem, IChatSystem {
    private IChatModel chatModel;
    private ITaskModel taskModel;

    public IEnumerator WaitResume() {
        while (chatModel.Pause.Value) {
            yield return null; // 等待下一帧

            // 判断不是任务场景，则退出协程
            if (SceneManager.GetActiveScene().name != SceneID.Task.ToString()) yield break;
        }
    }

    public async Task WaitResumeAsync() {
        while (chatModel.Pause.Value) await Task.Yield(); // 等待下一帧
    }

    protected override void OnInit() {
        chatModel = this.GetModel<IChatModel>();
        taskModel = this.GetModel<ITaskModel>();
    }
}
