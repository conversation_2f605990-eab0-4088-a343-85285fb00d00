﻿using QFramework;

public class ChangeCharacterCommand : AbstractCommand {
    private readonly string _characterInfo;

    public ChangeCharacterCommand(string characterInfo) {
        _characterInfo = characterInfo;
    }

    protected override async void OnExecute() {
        await this.GetSystem<IIndexCharacterSystem>().MMCharacterStyle.ChangeCharacter(_characterInfo);
        this.SendEvent(new ChangeCharacterEvent(_characterInfo));
    }
}
