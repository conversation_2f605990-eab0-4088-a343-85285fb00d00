using System;
using System.Collections.Generic;
using System.Linq;
using Builtin.Utility;
using QFramework;
using UnityEngine;

public class GameHelper : MonoBehaviour, IController {
    public int fontSize = 40;

    private GUIStyle fontStyle;
    private bool alwaysScrollToBottom;


    #region Road

    private bool roadDir;

    #endregion

    private void Awake() {
        Application.logMessageReceivedThreaded += HandleLogThreaded;
    }

    private void Start() {
        isLogPanelVisible = openOnStart;

        fontStyle = new GUIStyle();
        fontStyle.fontSize = fontSize; // 字体大小
    }

    private void Update() {
        UpdateQueuedLogs();
        if (Input.GetKeyDown(toggleKey)) isLogPanelVisible = !isLogPanelVisible;
    }

    private void OnDestroy() {
        Application.logMessageReceivedThreaded -= HandleLogThreaded;
    }

    private void OnGUI() {
        if (this.GetSystem<INetworkSystem>().ServerType == ServerTypeAot.Release) return;

        if (!isLogPanelVisible) {
            // 不显示log
            var width = 300.0f;
            var btnHeight = 45.0f;
            GUILayout.Window(0, new Rect(Screen.width / 2 - width, 0, width, windowHeight), windowID => {
                if (GUILayout.Button("Show", GUILayout.Width(width), GUILayout.Height(btnHeight)))
                    SetLogPanelStatus(true);

                windowHeight = 30.0f;
            }, windowTitle);
        } else {
            // 显示log
            windowRect = GUILayout.Window(1, windowRect, DrawWindow, windowTitle);
        }
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    public void SetLogPanelStatus(bool show) {
        isLogPanelVisible = show;
    }

    /************************************* Log ****************************************/
    private void UpdateQueuedLogs() {
        LogInfo log;
        while (queuedLogs.TryDequeue(out log)) ShowLogItem(log);
    }

    private void ShowLogItem(LogInfo log) {
        var lastLog = logs.Count == 0 ? null : logs.Last();
        var isDuplicateOfLastLog = log != null ? log.EqualsTo(lastLog) : false;

        if (isDuplicateOfLastLog) {
            log.count = lastLog.count + 1;
            logs[logs.Count - 1] = log;
        } else {
            logs.Add(log);
            if (clampLogCount) RemoveUnnecessaryLogs();
        }
    }

    private void DrawWindow(int windowID) {
        DrawToolbar();
        DrawLogList();
    }

    private void DrawLogList() {
        // 记录原始样式
        float originalScrollbarWidth = GUI.skin.verticalScrollbar.fixedWidth;
        float originalThumbWidth = GUI.skin.verticalScrollbarThumb.fixedWidth;

        // 设置滚动条和滑块的宽度
        GUI.skin.verticalScrollbar.fixedWidth = 50f;
        GUI.skin.verticalScrollbarThumb.fixedWidth = 50f;

        scrollPosition = GUILayout.BeginScrollView(scrollPosition);
        GUILayout.BeginVertical();
        // filter logs
        var visibleLogs = logs.Where(log => logTypeFilters[log.type]).ToList();
        foreach (var log in visibleLogs) DrawLog(log);
        GUILayout.EndVertical();
        var innerScrollRect = GUILayoutUtility.GetLastRect();
        GUILayout.EndScrollView();

        // 恢复原始样式
        GUI.skin.verticalScrollbar.fixedWidth = originalScrollbarWidth;
        GUI.skin.verticalScrollbarThumb.fixedWidth = originalThumbWidth;

        var outerScrollRect = GUILayoutUtility.GetLastRect();
        if (Event.current.type == EventType.Repaint && (alwaysScrollToBottom || IsScrolledToBottom(innerScrollRect, outerScrollRect)))
            ScrollToBottom();

        GUI.contentColor = Color.white;
    }


    private void DrawLog(LogInfo log) {
        GUI.contentColor = logTypeColors[log.type];
        if (isCollapsed)
            DrawCollapsedLog(log);
        else
            DrawExpandedLog(log);
    }

    private void DrawCollapsedLog(LogInfo log) {
        GUILayout.BeginHorizontal();

        GUILayout.Label(log.GetFinalMessage(), fontStyle);
        GUILayout.FlexibleSpace();
        GUILayout.Label(log.count.ToString(), GUI.skin.box);

        GUILayout.EndHorizontal();
    }

    private void DrawExpandedLog(LogInfo log) {
        for (var i = 0; i < log.count; i += 1) GUILayout.Label(log.GetFinalMessage(), fontStyle);
    }

    private void DrawToolbar() {
        GUILayout.BeginHorizontal();

        if (GUILayout.Button(clearLabel, GUILayout.Height(50))) logs.Clear();
        if (GUILayout.Button(HideLabel, GUILayout.Height(50))) SetLogPanelStatus(false);

        Color originalColor = GUI.color;
        if (alwaysScrollToBottom) GUI.color = Color.green;

        if (GUILayout.Button(scrollToBottomLabel, GUILayout.Height(50))) {
            alwaysScrollToBottom = !alwaysScrollToBottom;
        }

        GUI.color = originalColor;

        foreach (LogType logType in Enum.GetValues(typeof(LogType))) {
            var currentState = logTypeFilters[logType];
            var label = logType.ToString();
            logTypeFilters[logType] = GUILayout.Toggle(currentState, label, GUILayout.ExpandWidth(false));
            GUILayout.Space(20);
        }

        isCollapsed = GUILayout.Toggle(isCollapsed, collapseLabel, GUILayout.ExpandWidth(false));

        GUILayout.EndHorizontal();
    }

    private bool IsScrolledToBottom(Rect innerScrollRect, Rect outerScrollRect) {
        var innerScrollHeight = innerScrollRect.height;

        var outerScrollHeight = outerScrollRect.height - GUI.skin.box.padding.vertical;

        if (outerScrollHeight > innerScrollHeight) return true;

        return Mathf.Approximately(innerScrollHeight, scrollPosition.y + outerScrollHeight);
    }

    private void ScrollToBottom() {
        scrollPosition = new Vector2(0, int.MaxValue);
    }

    private void RemoveUnnecessaryLogs() {
        var amountToRemove = logs.Count - maxLogCount;
        if (amountToRemove <= 0) return;
        logs.RemoveRange(0, amountToRemove);
    }

    private void HandleLogThreaded(string message, string stackTrace, LogType type) {
        var log = new LogInfo {
            count = 1,
            message = message,
            stackTrace = stackTrace,
            type = type
        };
        if (message.Contains("ListenContactData") ||
            message.Contains("DTC.EnterTechBleManager") ||
            message.Contains("设备配置是否正常") ||
            message.Contains("DTC") ||
            message.Contains("OnListenBatteryCallBack") ||
            message.Contains("BluetoothDeviceScript")) return;
        queuedLogs.Enqueue(log);
    }

    #region Log

    public KeyCode toggleKey = KeyCode.BackQuote;
    public bool openOnStart;
    public bool clampLogCount;
    public int maxLogCount = 1000;

    private readonly GUIContent clearLabel = new("Clear", "Clear the contents of the console.");
    private readonly GUIContent HideLabel = new("Hide", "Hide the contents");
    private readonly GUIContent scrollToBottomLabel = new("Auto Bottom", "Toggle automatic scroll to bottom.");
    private readonly GUIContent collapseLabel = new("Collapse", "Hide repeated messages.");

    private bool isCollapsed;
    private bool isLogPanelVisible;
    private Vector2 scrollPosition;

    private readonly Dictionary<LogType, Color> logTypeColors = new() {
        { LogType.Assert, Color.white },
        { LogType.Error, Color.red },
        { LogType.Exception, Color.red },
        { LogType.Log, Color.white },
        { LogType.Warning, Color.yellow }
    };

    private readonly Dictionary<LogType, bool> logTypeFilters = new() {
        { LogType.Assert, true },
        { LogType.Error, true },
        { LogType.Exception, true },
        { LogType.Log, true },
        { LogType.Warning, true }
    };

    private readonly List<LogInfo> logs = new();
    private readonly ConcurrentQueue<LogInfo> queuedLogs = new();

    #endregion

    #region Normal

    private const int margin = 50;
    private const string windowTitle = "GameHelper";
    private float windowHeight = 150;
    private Rect windowRect = new(margin, margin, Screen.width - margin * 2, Screen.height - margin * 2);

    #endregion

    /************************************* Log ****************************************/
}

public class LogInfo {
    private const int MaxMessageLength = 16382;
    public int count;
    public string message;
    public string stackTrace;
    public LogType type;

    public bool EqualsTo(LogInfo log) {
        if (log == null)
            return false;
        return message == log.message && stackTrace == log.stackTrace && type == log.type;
    }

    public string GetFinalMessage() {
        if (string.IsNullOrEmpty(message))
            return "";
        return message.Length <= MaxMessageLength ? message : message.Substring(0, MaxMessageLength);
    }
}

public class ConcurrentQueue<T> {
    private readonly Queue<T> queue = new();
    private readonly object queueLock = new();

    public void Enqueue(T item) {
        lock (queueLock) {
            queue.Enqueue(item);
        }
    }

    public bool TryDequeue(out T result) {
        lock (queueLock) {
            if (queue.Count == 0) {
                result = default;
                return false;
            }

            result = queue.Dequeue();
            return true;
        }
    }
}
//```

//### 主要更改点：
//1. * *日志列表的预处理 * *：在 `Update` 方法中处理日志队列，确保在 `OnGUI` 方法中使用一致的日志列表状态。
//2. **日志项的过滤和重复检测**：在 `ShowLogItem` 方法中，确保在 `Repaint` 事件之外处理日志项的添加和更新。
//3. **日志项的动态添加**：在 `HandleLogThreaded` 方法中，将新的日志项添加到 `queuedLogs` 队列中，并在 `Update` 方法中处理日志队列。

//这些更改应该可以解决你遇到的 `ArgumentException` 错误。如果还有其他问题，请随时告诉我！
