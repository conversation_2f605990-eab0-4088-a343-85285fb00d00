﻿using System;
using Cysharp.Threading.Tasks;
using QFramework;
using UnityEngine.AddressableAssets;
using UnityEngine.ResourceManagement.AsyncOperations;

/// <summary>
///     定义一个可寻址系统的接口，继承自ISystem接口
/// </summary>
public interface IAddressableSystem : ISystem {
    /// <summary>
    ///     异步加载指定路径的资源
    ///     调用后立即返回，不会阻塞主线程，适合在需要保持应用响应性（如UI流畅）的场景中使用。
    ///     对主线程性能影响小，适合加载大型资源，如高分辨率纹理、复杂模型等，能避免卡顿。
    ///     适合需要非阻塞加载的场景，如游戏运行时加载资源，保持游戏流畅。
    ///     需要async和await支持，调用时要处理异步逻辑，代码较复杂。
    /// </summary>
    /// <typeparam name="T">资源的类型</typeparam>
    /// <param name="path">资源的路径</param>
    /// <returns>返回一个异步操作句柄的UniTask</returns>
    public UniTask<AsyncOperationHandle<T>> LoadAssetAsync<T>(string path);

    public UniTaskVoid LoadAssetCb<T>(string path, Action<AsyncOperationHandle<T>> OnLoaded);

    /// <summary>
    ///     同步加载指定路径的资源
    ///     会一直等待资源加载完成，期间主线程被阻塞，可能导致应用卡顿。
    ///     可能导致主线程卡顿，尤其在加载大资源时，影响应用响应性。
    ///     适合简单脚本或工具中快速加载小型资源，如配置文件、小型纹理等，代码更简洁。
    ///     直接返回结果，调用简单，无需处理异步逻辑，但灵活性低。
    /// </summary>
    /// <typeparam name="T">资源的类型</typeparam>
    /// <param name="path">资源的路径</param>
    /// <returns>返回一个异步操作句柄</returns>
    public AsyncOperationHandle<T> LoadAsset<T>(string path);
}

/// <summary>
///     可寻址系统的实现类，继承自AbstractSystem并实现IAddressableSystem接口
/// </summary>
public class AddressableSystem : AbstractSystem, IAddressableSystem {
    public async UniTask<AsyncOperationHandle<T>> LoadAssetAsync<T>(string path) {
        var obj = Addressables.LoadAssetAsync<T>(path);
        await obj;
        return obj;
    }

    public AsyncOperationHandle<T> LoadAsset<T>(string path) {
        var obj = Addressables.LoadAssetAsync<T>(path);
        obj.WaitForCompletion();
        return obj;
    }

    public async UniTaskVoid LoadAssetCb<T>(string path, Action<AsyncOperationHandle<T>> OnLoaded) {
        var obj = Addressables.LoadAssetAsync<T>(path);
        await obj;
        //Debug.Log("obj.Status:" + obj.Status);
        if (obj.Status == AsyncOperationStatus.Succeeded) OnLoaded(obj);
    }

    /// <summary>
    ///     系统初始化方法
    /// </summary>
    protected override void OnInit() {
        // 目前为空，可以添加初始化逻辑
    }
}
