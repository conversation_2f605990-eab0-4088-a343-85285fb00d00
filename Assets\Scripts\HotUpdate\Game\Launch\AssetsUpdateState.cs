using QFramework;

public class AssetsUpdateState : AbstractState<LaunchStates, Launch>, IController {
    public AssetsUpdateState(FSM<LaunchStates> fsm, Launch target) : base(fsm, target) {
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    protected override void OnEnter() {
        this.SendCommand(new ShowPageCommand(UIPageType.GameHelper, UILevelType.Debug));
        mFSM.ChangeState(LaunchStates.InitConfig);
    }
}
