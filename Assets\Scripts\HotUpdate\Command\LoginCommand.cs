﻿using QFramework;
using UnityEngine;
using Utils;

public class LoginCommand : AbstractCommand {
    private readonly bool mAutoLogin;
    private readonly bool mIsRemember;
    private readonly string mPassword;
    private readonly string mUserName;

    public LoginCommand(string userName, string password, bool isRemember, bool autoLogin = false) {
        mUserName = userName;
        mPassword = password;
        mIsRemember = isRemember;
        mAutoLogin = autoLogin;
    }

    protected override async void OnExecute() {
        var requsetData = new LoginRequest {
            username = mUserName,
            password = Util.GetSha256(mPassword)
        };

        var request = await this.GetSystem<INetworkSystem>().PostJson<UserInfoResponse>(
            RequestUrl.loginUrl,
            requsetData,
            this.GetModel<IUserModel>().Token.Value);

        if (request.IsSuccess) {
            this.GetModel<IUserModel>().RememberPassword.Value = mIsRemember ? 1 : 0;
            this.GetSystem<IDataParseSystem>().ParseSelfUserInfo(request.Data);
            this.GetModel<IUserModel>().Password.Value = mPassword;
        }

        var code = (int)request.StatusCode;
        if (code == 200) {
            var info = new WarningAlertInfo("Login Success", () => {
                this.SendCommand<RecordLoginCommand>();
                this.SendCommand(new LoadSceneCommand(ScenePath.Index));
            });
            this.SendEvent(new ShowPageEvent(UIPageType.WarningAlert, UILevelType.Alart, info));
            return;
        }

        if (code == 423) {
            var info = new WarningAlertInfo("Password Error");
            this.SendEvent(new ShowPageEvent(UIPageType.WarningAlert, UILevelType.Alart, info));
        } else if (code == 404) {
            var alertInfo = new WarningAlertInfo("Account Number not registered");
            this.SendEvent(new ShowPageEvent(UIPageType.WarningAlert, UILevelType.Alart, alertInfo));
        } else {
            Debug.LogError(RequestUrl.loginUrl + ": " + request.StatusCode +
                           " " + request.Data + " " + request.ErrorMessage);
            if (request.StatusCode is not (401 or 204 or 203)) {
                Debug.LogError(RequestUrl.recordLogin + ": " + request.StatusCode +
                               " " + request.Data + " " + request.ErrorMessage);
                var info = new InfoConfirmInfo("", "啊哦~服务器出错！\n请联系管理员或重启APP！\n点击确定后，将退出APP。",
                    Application.Quit
                    , type: ConfirmAlertType.Single);
                this.SendEvent(new ShowPageEvent(UIPageType.InfoConfirmAlert, UILevelType.Alart, info));
            }
        }

        if (mAutoLogin) {
            this.GetModel<IUserModel>().RememberPassword.Value = 0;
            this.SendCommand(new ShowPageCommand(UIPageType.LoginAtisUI));
        }
    }
}
