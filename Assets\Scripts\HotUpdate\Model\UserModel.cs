﻿using System;
using QFramework;
using UnityEngine;

[Serializable]
public class UserInfo {
    public int uid;
    public string username;
    public string name;
    public int aid;
    public int star;
    public bool gender;
    public int age;
}

public interface IUserModel : IModel {
    public BindableProperty<string> UserName { get; } // 用户名
    public BindableProperty<string> Name { get; } // 姓名
    public BindableProperty<string> Password { get; } // 密码
    public BindableProperty<int> Aid { get; } // 头像id
    public BindableProperty<int> Uid { get; } // 用户id
    public BindableProperty<int> Star { get; } // TODO 钱 奖赏系统
    public BindableProperty<bool> Gender { get; } // 性别 {0：男， 1：女}
    public BindableProperty<int> Age { get; } // 年龄
    public BindableProperty<int> RememberPassword { get; } // 是否记住密码
    public BindableProperty<bool> IsCompleteGuidance { get; } // 是否完成新手教程
    public BindableProperty<string> Token { get; }
    public BindableProperty<string> UserInfo { get; }
    public BindableProperty<bool> IsSuperuser { get; }
    public BindableProperty<string> Cid { get; set; }

    public void SetUserInfoPart(UserInfo userInfo);
    public UserInfo GetUserInfo();
}

public class UserModel : AbstractModel, IUserModel {
    public BindableProperty<string> UserName { get; } = new();
    public BindableProperty<string> Name { get; } = new();
    public BindableProperty<string> Password { get; } = new();
    public BindableProperty<int> Aid { get; } = new();
    public BindableProperty<int> Uid { get; } = new();
    public BindableProperty<int> Star { get; } = new();
    public BindableProperty<bool> Gender { get; } = new();
    public BindableProperty<int> Age { get; } = new();
    public BindableProperty<int> RememberPassword { get; } = new();
    public BindableProperty<bool> IsCompleteGuidance { get; } = new();
    public BindableProperty<string> Token { get; } = new();
    public BindableProperty<string> UserInfo { get; } = new();
    public BindableProperty<bool> IsSuperuser { get; } = new();
    public BindableProperty<string> Cid { get; set; } = new();

    public UserInfo GetUserInfo() {
        var userInfo = new UserInfo();
        userInfo.uid = Uid.Value;
        userInfo.username = UserName.Value;
        userInfo.aid = Aid.Value;
        userInfo.name = Name.Value;
        userInfo.star = Star.Value;
        userInfo.gender = Gender.Value;
        userInfo.age = Age.Value;
        return userInfo;
    }

    public void SetUserInfoPart(UserInfo userInfo) {
        Name.Value = userInfo.name;
        UserName.Value = userInfo.username;
        Uid.Value = userInfo.uid;
        Star.Value = userInfo.star;
        Gender.Value = userInfo.gender;
        Age.Value = userInfo.age;
    }

    protected override void OnInit() {
        UserName.Value = PlayerPrefs.GetString(PrefKeys.userName);
        UserName.Register(v => {
            if (PlayerPrefs.GetInt(PrefKeys.rememberPassword) == 1) PlayerPrefs.SetString(PrefKeys.userName, v);
        });

        Password.Value = PlayerPrefs.GetString(PrefKeys.password);
        Password.Register(v => {
            if (PlayerPrefs.GetInt(PrefKeys.rememberPassword) == 1) PlayerPrefs.SetString(PrefKeys.password, v);
        });

        RememberPassword.Value = PlayerPrefs.GetInt(PrefKeys.rememberPassword);
        RememberPassword.Register(v =>
            PlayerPrefs.SetInt(PrefKeys.rememberPassword, v)
        );

        IsCompleteGuidance.Value = PlayerPrefs.GetInt(PrefKeys.isCompleteGuidance) == 1;
        IsCompleteGuidance.Register(v =>
            PlayerPrefs.SetInt(PrefKeys.isCompleteGuidance, v ? 1 : 0)
        );

        Aid.Value = PlayerPrefs.GetInt(PrefKeys.Aid);
        Aid.Register(v =>
            PlayerPrefs.SetInt(PrefKeys.Aid, v)
        );

        // 超级管理员功能
        IsSuperuser.Value = PlayerPrefs.GetInt(PrefKeys.isSuperuser) == 1;
        IsSuperuser.Register(v => {
            PlayerPrefs.SetInt(PrefKeys.isSuperuser, v ? 1 : 0);
            this.SendEvent(new HandleExitButtonActiveEvent());
        });

        Cid.Value = PlayerPrefs.GetString(PrefKeys.CharacterId);
        if (Cid.Value.IsTrimNullOrEmpty()) Cid.Value = "001"; // 初始为 001
        Cid.Register(v =>
            PlayerPrefs.SetString(PrefKeys.CharacterId, v)
        );
    }
}
