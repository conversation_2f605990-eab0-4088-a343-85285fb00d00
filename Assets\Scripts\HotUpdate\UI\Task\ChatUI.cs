using System;
using System.Collections;
using System.IO;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using QFramework;
using UnityEngine;
using UnityEngine.UI;
using Utils;

public class ChatUI : MonoBehaviour, IController {
    private IAudioManagerSystem audioManagerSystem;
    private IWebSocketSystem _ws;

    private IChatModel chatModel;
    private IEegModel eegModel;

    private BluetoothEEG bluetoothEEG;

    /// <summary>
    ///     摄像头服务
    /// </summary>
    private IWebCaptureSystem m_VideoSystem;

    private void Awake() {
        chatModel = this.GetModel<IChatModel>();
        eegModel = this.GetModel<IEegModel>();

        audioManagerSystem = this.GetSystem<IAudioManagerSystem>();
        m_VideoSystem = this.GetSystem<IWebCaptureSystem>();
        _ws = this.GetSystem<IWebSocketSystem>();
        if (Application.platform == RuntimePlatform.Android)
            bluetoothEEG = GameObject.Find("BluetoothEEG").GetComponent<BluetoothEEG>();

        // m_CommitMsgBtn.onClick.AddListener(() => {
        //     this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
        //     if (string.IsNullOrWhiteSpace(m_InputWord.text)) {
        //         print("文本为null或空，或仅包含空白字符.");
        //         return;
        //     }
        //     this.SendCommand<UserSpeakEndCommand>();
        // });

        // m_TestBtn.onClick.AddListener(() =>
        // {
        //     this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
        //     testShow.SetActive(!testShow.activeSelf);
        // });

        m_PauseBtn.onClick.AddListener(() => { this.SendCommand(new TaskPauseResumeCommand(true)); });

        #region 切换对话模式

        m_TextInputBtn.onClick.AddListener(() => { this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low); });
        m_VoiceInputBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            ShowRecordMode();
        });
        m_RecordInputBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            ShowVoiceMode();
        });

        #endregion

        m_BluetoothBtn.onClick.AddListener(() => {
            m_PauseBtn.onClick.Invoke();
            // TODO: 对话场景下，无需，也不能进行脑机校准，需要隐藏
            StartCoroutine(ShowEEGBluetoothUIAfterDelay());
        });

        this.RegisterEvent<TaskPauseResumeEvent>(OnClickPauseResume).UnRegisterWhenGameObjectDestroyed(gameObject);
        this.RegisterEvent<TaskQuitEvent>(IsQuit).UnRegisterWhenGameObjectDestroyed(gameObject);
        this.RegisterEvent<TaskStartEvent>(TaskStart).UnRegisterWhenGameObjectDestroyed(gameObject);
        this.RegisterEvent<ExitTaskEvent>(TaskExit).UnRegisterWhenGameObjectDestroyed(gameObject);
        this.RegisterEvent<ChatOverEvent>(ChatOver).UnRegisterWhenGameObjectDestroyed(gameObject);
        this.RegisterEvent<ChatTimeOutEvent>(ChatTimeOut).UnRegisterWhenGameObjectDestroyed(gameObject);
        this.RegisterEvent<AvatarSpeakEndEvent>(e => {
            var interactable = !chatModel.IsTimeout;
            m_CommitMsgBtn.interactable = interactable;
            m_InputWord.interactable = interactable;
        }).UnRegisterWhenGameObjectDestroyed(gameObject);
        this.RegisterEvent<UserSpeakEndEvent>(e => {
            m_CommitMsgBtn.interactable = false;
            m_InputWord.interactable = false;
        }).UnRegisterWhenGameObjectDestroyed(gameObject);
        this.RegisterEvent<AvatarFeedbackEvent>(e => {
            SetAnimator("state", e.m_state);
        }).UnRegisterWhenGameObjectDestroyed(gameObject);
    }

    public void Update() {
        JudgeNet();
        //  TODO： 不好记录，容易重复记录，后期优化
        // chatModel.ArchiveInfoDic.Value.EventTimeStamp.Add((eegModel.EegStatus(), Time.time - chatModel.OneChatStartTimestamp));

        // TODO： 后期强制用户佩戴脑电设备，再显示此按钮。 记得把 EegStatusText 在ChatUI上显示出来
        // if (eegModel.eegStatus.Value == EegStatusEnum.Connected || eegModel.eegStatus.Value == EegStatusEnum.Inactive)
        //     m_BluetoothBtn.gameObject.SetActiveFast(false);
        // else
        //     m_BluetoothBtn.gameObject.SetActiveFast(true);
    }

    public void OnEnable() {
        this.RegisterEvent<ChangeCharacterSucceedEvent>(e => {
            Debug.Log("receive ChangeCharacterSucceedEvent");
            m_kid = e.mCharacter;
            _headAnimation = m_kid.transform.RecursiveFindChild("HEAD").gameObject.AddComponent<Animation>();
            _headAnimation.AddClip(blinkAnimationClip, blinkAnimationClip.name);
            _headAnimation.Play(blinkAnimationClip.name);

            m_Animator = m_kid.transform.RecursiveFindChild("KekosCharacter").gameObject.GetComponent<Animator>();
            if (m_Animator == null) Debug.LogWarning("Animator component not found on the character GameObject.");
            // 初始化model，并开始对话
            this.SendCommand(new TaskStartCommand());
        }).UnRegisterWhenDisabled(gameObject);

        this.GetSystem<ISoundSystem>().SetBackgroundMusic(false);
        // root权限
        // Debug.Log("root权限 m_TestBtn");
        // if (this.GetModel<IUserModel>().IsSuperuser.Value) m_TestBtn.gameObject.SetActive(true);

        m_BluetoothBtn.gameObject.SetActiveFast(false);

        Debug.Log("设置对话方式");

        switch (chatModel.chatType.Value) {
            case ChatType.TextInput:
                ShowTextMode();
                break;
            case ChatType.RecordInput:
                ShowRecordMode();
                break;
            case ChatType.VoiceInput:
                ShowVoiceMode();
                break;
        }

        Debug.Log("ChangeCharacterCommand");
        this.SendCommand(new ChangeCharacterCommand(this.GetModel<IUserModel>().Cid.Value));
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    private void ChatTimeOut(ChatTimeOutEvent e) {
        m_RecordInputBtn.interactable = false;
        m_TextInputBtn.interactable = false;
        m_VoiceInputBtn.interactable = false;
    }

    private void TaskStart(TaskStartEvent e) {
        m_RecordTipsText.text = "";
        m_InputWord.text = "";
        m_TextBack.text = "";

        m_RecordInputBtn.interactable = true;
        m_TextInputBtn.interactable = true;
        m_VoiceInputBtn.interactable = true;

        m_ThemeText.text = e.taskName;

        audioManagerSystem.ResetPlayback();
        audioManagerSystem.audioSource.Play();

        m_CountDownTimer.StartCountdown(e.chatTime);
        audioManagerSystem.StartRecord();
        if (Application.platform == RuntimePlatform.Android) bluetoothEEG.StartRecord();
        m_VideoSystem.StartRecord(Util.ThemeDir, $"video.{chatModel.FileName.Value}");

        var wsUrl = this.GetSystem<IWebSocketSystem>().GetWebSocketUrl();
        this.GetSystem<IVoiceAssistantClientSystem>().WebSocket_IOT_Mqtt_Microphone_InIt(wsUrl, this);
    }

    private async void ChatOver(ChatOverEvent e) {
        Debug.Log("ChatOver");

        // 将交互按钮设为不可交互
        m_RecordBtn.interactable = false;
        m_InputWord.interactable = false;
        m_CommitMsgBtn.interactable = false;

        SetAnimator("state", 3);

        await UniTask.Delay(1000);

        CloseService(() =>
            this.SendCommand(new SaveDataAndUploadCommand(e.m_chatOverStates)));
    }

    #region UI定义

    /// <summary>
    ///     聊天UI窗
    /// </summary>
    [SerializeField] private GameObject m_ChatPanel;

    /// <summary>
    ///     输入的信息
    /// </summary>
    [SerializeField] public InputField m_InputWord;

    /// <summary>
    ///     返回的信息
    /// </summary>
    [SerializeField] private Text m_TextBack;

    /// <summary>
    ///     发送信息按钮
    /// </summary>
    [SerializeField] private Button m_CommitMsgBtn;

    /// <summary>
    ///     倒计时显示
    /// </summary>
    [Header("倒计时显示")] [SerializeField] public CountDownTimer m_CountDownTimer;

    [SerializeField] public Text m_ThemeText;

    /// <summary>
    ///     动画控制器
    /// </summary>
    private Animator m_Animator;

    private Animation _headAnimation;
    private GameObject m_kid;

    /// <summary>
    ///     眨眼动画
    /// </summary>
    [SerializeField] private AnimationClip blinkAnimationClip;

    public Button m_PauseBtn;

    public Button m_TextInputBtn;
    public Button m_VoiceInputBtn;
    public Button m_RecordInputBtn;

    public GameObject m_InputText;
    public GameObject m_AudioVisual;
    public Button m_RecordBtn;

    public GameObject m_RecordTips;
    public Text m_RecordTipsText;

    public Button m_BluetoothBtn;

    /// <summary>
    ///     摄像头画面
    /// </summary>
    public RawImage UICamera;

    #endregion

    #region root

    public Button m_TestBtn;
    public GameObject testShow;
    public Toggle isSpeechTog;
    public Toggle stateTog;
    public Toggle isChattingTog;

    #endregion

    #region 语音输出

    public void SetText(string text="") {
        m_TextBack.text = text;
    }

    public void AddText(string text) {
        m_TextBack.text += text;
    }

    public void SetTipsText(string text) {
        m_RecordTipsText.text = text;
    }

    #endregion

    #region 其他函数

    public void SetAnimator(string _para, int _value) {
        if (m_Animator == null) {
            Debug.Log("m_Animator == null _value: " + _value);
            return;
        }

        m_Animator.SetInteger(_para, _value);
    }

    private void OnClickPauseResume(TaskPauseResumeEvent e) {
        this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
        chatModel.Pause.Value = e.pause;

        if (e.pause) {
            this.SendCommand(new ShowPageCommand(UIPageType.PauseUI));
            audioManagerSystem.audioSource.Pause();
        } else {
            audioManagerSystem.audioSource.UnPause();
        }
    }

    private IEnumerator ShowEEGBluetoothUIAfterDelay() {
        yield return new WaitForSeconds(0.5f); // 等待0.5秒
        this.SendCommand(new ShowPageCommand(UIPageType.EEGBluetoothUI, UILevelType.UIPage));
    }

    /// <summary>
    ///     关闭所有服务；关闭所有协程；跳转到主场景页面
    ///     目前有两种情况会调用此处
    ///     1. 在任务过程中主动退出 e.quit=true
    ///     2. 在任务结束后，在TaskResultUI中主动退出 e.quit=false
    ///     这里的 e.quit 是是否在任务中中断的含义
    /// </summary>
    /// <param name="e"></param>
    private void IsQuit(TaskQuitEvent e) {
        Debug.Log("IsQuit");
        chatModel.IsQuit = true;

        CloseService(); // 最后再次保证，将服务都关闭

        // StopAllCoroutines();  // TODO 测试，关闭所有协程，是否有问题。 可能对后续开发有影响 对上传数据可能有影响
        this.SendCommand(new LoadSceneCommand(ScenePath.Index)); // 切换场景到主页面
    }

    /// <summary>
    ///     暂未使用
    /// </summary>
    /// <param name="e"></param>
    private void TaskExit(ExitTaskEvent e) {
        // 1. 关闭服务
        CloseService();
    }

    /// <summary>
    ///     关闭各种服务 录音 录像 EEG
    /// </summary>
    private void CloseService(Action callback = null) {
        Debug.Log("CloseService");
        if (audioManagerSystem.audioSource.isPlaying) audioManagerSystem.audioSource.Stop();
        audioManagerSystem.StopRecord(Path.Combine(Util.ThemeDir, $"audio.{chatModel.FileName.Value}.wav"));
        if (Application.platform == RuntimePlatform.Android) {
            this.GetModel<IEegModel>().EEGPath = Path.Combine(Util.ThemeDir, $"EEG.{chatModel.FileName.Value}.txt");
            bluetoothEEG.StopRecord(this.GetModel<IEegModel>().EEGPath);
        } else {
            this.GetModel<IEegModel>().EEGPath = string.Empty;
        }

        if (JudgeNet()) return;  // 断网时，视频插件会出错，所以不进行保存
        m_VideoSystem.StopRecord();
        callback?.Invoke();
    }

    /// <summary>
    ///     判断是否断网，断网则退出 APP
    /// </summary>
    /// <returns></returns>
    private bool JudgeNet() {
        if (Application.internetReachability != NetworkReachability.NotReachable) return false;
        Debug.LogError("断网了...");
        _ws.Disconnect();
        audioManagerSystem.PlayLocalAudio(Util.NoNetPath, Util.NoNetText).ToCoroutine();
        var info = new InfoConfirmInfo("", Util.NoNetText, Util.AppQuit, type: ConfirmAlertType.Single);
        this.SendCommand(new ShowPageCommand(UIPageType.InfoConfirmAlert, UILevelType.Alart, info));
        return true;
    }

    private void ShowVoiceMode() {
        const ChatType toChatType = ChatType.VoiceInput;
        this.GetSystem<IVoiceAssistantClientSystem>().SwitchChatType(toChatType);
        m_TextInputBtn.gameObject.SetActiveFast(false);
        m_VoiceInputBtn.gameObject.SetActiveFast(true);
        m_RecordInputBtn.gameObject.SetActiveFast(false);

        // 切换为自由对话模式
        chatModel.chatType.Value = toChatType;
        m_RecordTips.SetActiveFast(true);
        m_InputText.SetActiveFast(false);
        m_AudioVisual.SetActiveFast(true);
        m_RecordBtn.gameObject.SetActiveFast(false);
    }

    private void ShowRecordMode() {
        const ChatType toChatType = ChatType.RecordInput;
        this.GetSystem<IVoiceAssistantClientSystem>().SwitchChatType(toChatType);
        m_TextInputBtn.gameObject.SetActiveFast(false);
        m_VoiceInputBtn.gameObject.SetActiveFast(false);
        m_RecordInputBtn.gameObject.SetActiveFast(true);

        // 切换为录音对话模式
        chatModel.chatType.Value = toChatType;
        m_RecordTips.SetActiveFast(true);
        m_InputText.SetActiveFast(false);
        m_AudioVisual.SetActiveFast(false);
        m_RecordBtn.gameObject.SetActiveFast(true);
    }

    private void ShowTextMode() {
        const ChatType toChatType = ChatType.TextInput;
        this.GetSystem<IVoiceAssistantClientSystem>().SwitchChatType(toChatType);
        m_TextInputBtn.gameObject.SetActiveFast(true);
        m_VoiceInputBtn.gameObject.SetActiveFast(false);
        m_RecordInputBtn.gameObject.SetActiveFast(false);

        // 切换为文本对话模式
        chatModel.chatType.Value = toChatType;
        m_RecordTips.SetActiveFast(false);
        m_InputText.SetActiveFast(true);
        m_AudioVisual.SetActiveFast(false);
        m_RecordBtn.gameObject.SetActiveFast(false);
    }

    #endregion
}
