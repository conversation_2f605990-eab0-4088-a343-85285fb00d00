﻿using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using LitJson;
using QFramework;
using UnityEngine;
using UnityEngine.ResourceManagement.AsyncOperations;
using Utils;

public interface II18NSystem : ISystem {
    public string CurrentLang { get; set; }
    public bool InitFinish { get; set; }
    public Dictionary<string, string> LangMap { get; set; }
    public Dictionary<string, Sprite> FlagsDic { get; set; }

    public UniTask InitTranslation();
    public string GetText(string key);
    public void RegisterText(I18NText t);
    public void UnregisterText(I18NText t);
}

public class I18NSystem : AbstractSystem, II18NSystem {
    private readonly List<I18NText> allTexts = new();
    private readonly string defaultLang = "zh-cn";

    private readonly Dictionary<string, JsonData> trans = new();
    private JsonData current_dict;
    public string CurrentLang { get; set; }
    public bool InitFinish { get; set; }
    public Dictionary<string, string> LangMap { get; set; } = new();
    public Dictionary<string, Sprite> FlagsDic { get; set; } = new();

    public async UniTask InitTranslation() {
        var result = await this.GetSystem<IAddressableSystem>()
            .LoadAssetAsync<TextAsset>(Util.baseLanguagePath + "url.json");
        if (result.Status != AsyncOperationStatus.Succeeded) {
            Debug.LogError("Load url.json failed");
            return;
        }

        var fileNames = JsonMapper.ToObject(result.Result.text);
        var names = ((string)fileNames["FileName"]).Split(',');

        for (var i = 0; i < names.Length; i++) {
            var lanObj = await this.GetSystem<IAddressableSystem>()
                .LoadAssetAsync<TextAsset>(Util.baseLanguagePath + names[i]);
            if (lanObj.Status != AsyncOperationStatus.Succeeded) continue;

            var d = JsonMapper.ToObject(lanObj.Result.text);
            LangMap[(string)d["languageName"]] = (string)d["id"];
            trans[(string)d["id"]] = d;
            var flagIconUrl = Util.baseFlagPath + (string)d["flagName"] + ".png";
            var flagObj = await this.GetSystem<IAddressableSystem>().LoadAssetAsync<Sprite>(flagIconUrl);
            if (flagObj.Status == AsyncOperationStatus.Succeeded) FlagsDic[(string)d["languageName"]] = flagObj.Result;
        }

        InitFinish = true;

        try {
            ChangeLang(string.IsNullOrEmpty(this.GetModel<ISettingsModel>().Language.Value)
                ? defaultLang
                : this.GetModel<ISettingsModel>().Language.Value);
        } catch (Exception e) {
            Console.WriteLine(e);
            ChangeLang(defaultLang);
        }
    }

    public string GetText(string key) {
        try {
            var s = (string)current_dict[key];
            return s;
        } catch {
            // Debug.Log("没有key是 [ " + key + "]   |的翻译");
            return "";
        }
    }

    public void RegisterText(I18NText t) {
        allTexts.Add(t);
        t.Reset();
    }

    public void UnregisterText(I18NText t) {
        allTexts.Remove(t);
    }

    private void ChangeLang(string id) {
        Debug.Log("ChangeLang = " + id);
        if (!InitFinish) return;

        current_dict = trans[id];
        CurrentLang = id;
        RefreshAllText();
    }

    private void RefreshAllText() {
        var arr = allTexts.ToArray();
        foreach (var i in arr)
            if (i == null)
                allTexts.Remove(i);
            else
                i.Reset();
    }

    protected override void OnInit() {
        this.RegisterEvent<ChangeSettingEvent>(e => { ChangeLang(this.GetModel<ISettingsModel>().Language.Value); });
    }
}
