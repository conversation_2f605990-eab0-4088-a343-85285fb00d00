using QFramework;

public class Atis : Architecture<Atis> {
    protected override void Init() {
        RegisterSystem<II18NSystem>(new I18NSystem());
        RegisterSystem<IDataParseSystem>(new DataParseSystem());
        RegisterSystem<ISoundSystem>(new SoundSystem());
        RegisterSystem<IAddressableSystem>(new AddressableSystem());
        RegisterSystem<IGuidanceSystem>(new GuidanceSystem());
        RegisterSystem<INetworkSystem>(new NetworkSystem());
        RegisterSystem<IWebSocketSystem>(new WebSocketSystem());
        RegisterSystem<IRootPwdSystem>(new RootPwdSystem());
        RegisterSystem<IChatSystem>(new ChatSystem());
        RegisterSystem<IVadSystem>(new WebRtcVadSystem());
        RegisterSystem<IAudioManagerSystem>(new AudioManagerSystem());
        RegisterSystem<IWebCaptureSystem>(new WebCaptureSystem());
        RegisterSystem<IIndexCharacterSystem>(new IndexCharacterSystem());
        RegisterSystem<IVoiceAssistantClientSystem>(new VoiceAssistantClientSystem());
        RegisterSystem<ICameraSystem>(new CameraSystem());
        // RegisterSystem<IEegSystem>(new EegSystem());

        RegisterModel<IAvatarModel>(new AvatarModel());
        RegisterModel<IEegModel>(new EegModel());
        RegisterModel<IGameModel>(new GameModel());
        RegisterModel<IRootModel>(new RootModel());
        RegisterModel<ISettingsModel>(new SettingsModel());
        RegisterModel<IChatModel>(new ChatModel());
        RegisterModel<ITaskModel>(new TaskModel());
        RegisterModel<IUserModel>(new UserModel());
        RegisterModel<ICharacterModel>(new CharacterModel());
        RegisterModel<IConfigModel>(new ConfigModel());
        RegisterModel<ILogModel>(new LogModel());
    }
}
