using QFramework;
using UnityEngine;

/// <summary>
///     任务指标统计工具
///     此处主要用于统计时间
/// </summary>
public class TaskMetricsTool : MonoBehaviour, IController {
    private IChatModel _chatModel;
    private IEegModel _eegModel;
    private bool _isTracking; // 正在记录
    private ITaskModel _taskModel;

    private void Awake() {
        _taskModel = this.GetModel<ITaskModel>();
        _chatModel = this.GetModel<IChatModel>();
        _eegModel = this.GetModel<IEegModel>();

        this.RegisterEvent<TaskStartEvent>(StartTimer).UnRegisterWhenGameObjectDestroyed(gameObject);
        this.RegisterEvent<ChatOverEvent>(Reset).UnRegisterWhenGameObjectDestroyed(gameObject);
    }


    private void Reset(ChatOverEvent e) {
        Debug.Log("TaskMetricsTool Reset");
        _isTracking = false;
    }

    private void Update() {
        if (_isTracking)
            if (!_chatModel.Pause.Value) {
                _taskModel.taskMetrics.dialogueValidTime += Time.deltaTime;
                if (_eegModel.eegStatus.Value == EegStatusEnum.Connected)
                    _taskModel.taskMetrics.eegValidTime += Time.deltaTime;
            }
    }


    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    private void StartTimer(TaskStartEvent e) {
        Debug.Log("TaskMetricsTool _isTracking=true");
        _taskModel.taskMetrics.dialogueValidTime = 0f;
        _taskModel.taskMetrics.eegValidTime = 0f;
        _isTracking = true;
    }
}
