using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using QFramework;
using UnityEngine;
using UnityEngine.ResourceManagement.AsyncOperations;
using <PERSON><PERSON><PERSON>;

public interface IAudioManagerSystem : ISystem {
    public string MicrophoneDevice { get; }
    public AudioClip MainClip { get; }
    public int recordFrameSize { get; }
    public int channels { get; }
    public bool isRecording { get; }
    public void StartRecord();
    public void StopRecord(string filePath = null);
    public float[] GetRecordedData(int sampleCount);
    public byte[] EncodeAudio(float[] pcmData);
    public float[] DecodeAudio(byte[] opusData, bool decodeFEC = false);
    public void ResetPlayback();
    public void PlayAudio(float[] pcmData);
    public void InitStreamingPlayback();
    public UniTask PlayLocalAudio(string path, string text, ChatUI chatUI=null);
    public bool JudgePlayStateEnd();
    public AudioSource audioSource { get; set; }
}

public class AudioManagerSystem : AbstractSystem, IAudioManagerSystem {
    private readonly object bufferLock = new();
    private readonly List<float> playbackBuffer = new();
    private int playFrameSize = 1440; // 播放帧大小（60ms at 24kHz）
    private int playSampleRate = 24000; // 播放采样率
    private readonly int sampleRate = 16000; // 录音采样率

    private OpusCodec _playCodec; // 用于播放解码

    // 编解码器
    private OpusCodec _recordCodec; // 用于录音编码

    // 播放相关
    public AudioSource audioSource { get; set; }
    private IChatModel chatModel;
    private IConfigModel configModel;
    private bool isNewSession;
    private int totalSamplesWritten = 0;
    private int samplesPlayed = 0;

    private AudioClip streamingClip;

    public bool isRecording { get; private set; }
    public int channels { get; } = 1; // 通道数
    public int recordFrameSize { get; } = 960; // 录音帧大小（60ms at 16kHz）

    public AudioClip MainClip { get; private set; }

    // 录音状态
    public string MicrophoneDevice { get; private set; }

    /// <summary>
    ///     开始录音
    /// </summary>
    public void StartRecord() {
        Debug.Log("StartRecord()");
        if (MicrophoneDevice.IsTrimNullOrEmpty()) return;

        try {
            MainClip = Microphone.Start(MicrophoneDevice, false, configModel.Voice.MicMaxRecordingSeconds,
                sampleRate);
            Debug.Log("Audio clip channels: " + MainClip.channels);
            isRecording = true;
            Debug.Log("Microphone Recording started...");
        } catch (Exception e) {
            Debug.LogError($"录音失败: {e.Message}");
        }
    }

    /// <summary>
    ///     停止录音并保存到文件
    /// </summary>
    /// <param name="filePath"></param>
    public void StopRecord(string filePath = null) {
        if (MicrophoneDevice.IsTrimNullOrEmpty()) return;
        if (isRecording) {
            int length;
            if (Time.time - chatModel.OneChatStartTimestamp >= configModel.Voice.MicMaxRecordingSeconds)
                length = MainClip.samples * MainClip.channels;
            else
                length = Microphone.GetPosition(MicrophoneDevice) * MainClip.channels;
            Microphone.End(MicrophoneDevice);
            isRecording = false;

            if (filePath != null) WavUtility.SaveAudioClip(MainClip, filePath, length);
            Debug.Log("Microphone Recording stopped and saved.");
        } else {
            // TODO 需要报错或记录！！
            Debug.LogWarning("麦克风未开启或提前关闭，未能保存此次录音");
        }
    }


    /// <summary>
    ///     获取当前录音数据
    /// </summary>
    public float[] GetRecordedData(int sampleCount) {
        if (!isRecording || MainClip == null) return null;

        var data = new float[sampleCount];
        var position = Microphone.GetPosition(null);

        if (position < 0 || MainClip.samples <= 0) return null;

        // 计算从哪个位置开始读取数据
        var startPosition = (position - sampleCount) % MainClip.samples;
        if (startPosition < 0) startPosition += MainClip.samples;

        MainClip.GetData(data, startPosition);

        // 如果数据长度不匹配，进行裁剪
        if (data.Length != recordFrameSize * channels) {
            var resizedData = new float[recordFrameSize * channels];
            var copyLength = Math.Min(data.Length, resizedData.Length);
            Array.Copy(data, resizedData, copyLength);
            data = resizedData;
        }

        return data;
    }

    /// <summary>
    ///     编码音频数据
    /// </summary>
    public byte[] EncodeAudio(float[] pcmData) {
        try {
            if (pcmData == null || pcmData.Length == 0) {
                Debug.LogError("PCM数据为空");
                return null;
            }

            return _recordCodec.Encode(pcmData);
        } catch (Exception e) {
            Debug.LogError($"编码异常：{e.Message}");
            return null;
        }
    }

    /// <summary>
    ///     解码Opus音频数据
    /// </summary>
    public float[] DecodeAudio(byte[] opusData, bool decodeFEC = false) {
        if (opusData == null || opusData.Length == 0) {
            Debug.LogError("Opus数据为空");
            return null;
        }

        try {
            return _playCodec.Decode(opusData, decodeFEC);
        } catch (Exception e) {
            Debug.LogError($"解码异常：{e.Message}");
            return null;
        }
    }

    /// <summary>
    ///     重置播放缓冲区
    /// </summary>
    public void ResetPlayback() {
        lock (bufferLock) {
            playbackBuffer.Clear();
            totalSamplesWritten = 0;
            samplesPlayed = 0;
            isNewSession = true;
            Debug.Log("播放缓冲区已重置");
        }
    }

    /// <summary>
    ///     播放音频数据
    /// </summary>
    public void PlayAudio(float[] pcmData) {
        if (pcmData == null || pcmData.Length == 0) {
            Debug.LogWarning("尝试播放空音频数据");
            return;
        }

        lock (bufferLock) {
            playbackBuffer.AddRange(pcmData);
            totalSamplesWritten += pcmData.Length;

            // 增加最大缓冲区大小到10秒
            var maxBufferSize = playSampleRate * 20; // 20秒缓冲
            if (playbackBuffer.Count > maxBufferSize) {
                var removeCount = playbackBuffer.Count - maxBufferSize;
                playbackBuffer.RemoveRange(0, removeCount);
                totalSamplesWritten = Math.Max(0, totalSamplesWritten - removeCount);
                Debug.LogWarning($"缓冲区过大，移除了{removeCount}个样本");
            }
        }
    }

    /// <summary>
    ///     初始化Opus编解码器
    /// </summary>
    private void InitializeCodecs() {
        try {
            // 创建录音编码器
            _recordCodec = new OpusCodec(
                sampleRate,
                channels,
                recordFrameSize
            );

            // 创建播放解码器
            _playCodec = new OpusCodec(
                playSampleRate,
                channels,
                playFrameSize
            );
        } catch (Exception e) {
            Debug.LogError($"Opus编解码器初始化失败: {e.Message}");
            throw;
        }
    }

    /// <summary>
    ///     初始化流式播放
    /// </summary>
    public void InitStreamingPlayback() {
        if (audioSource == null)
            audioSource = CoroutineController.manager.audioSource;

        if (streamingClip == null) {
            // 增加缓冲区长度到5秒，以提供更大的缓冲空间
            var bufferLength = playSampleRate * 3; // 修改为1秒缓冲
            streamingClip = AudioClip.Create("StreamingPlayback", bufferLength, channels, playSampleRate, true,
                OnAudioRead);
            audioSource.clip = streamingClip;
            audioSource.loop = true;
            audioSource.volume = 1f;
            audioSource.mute = false;
        }
    }

    private void OnAudioRead(float[] data)
    {
        lock (bufferLock)
        {
            if (isNewSession)
            {
                playbackBuffer.Clear();
                isNewSession = false;
            }

            int count = Mathf.Min(data.Length, playbackBuffer.Count);
            for (int i = 0; i < count; i++)
            {
                data[i] = playbackBuffer[i];
            }

            // 剩余部分填充静音
            for (int i = count; i < data.Length; i++)
            {
                data[i] = 0f;
            }

            if (count > 0)
            {
                playbackBuffer.RemoveRange(0, count);
                samplesPlayed += count;
            }
        }
    }

    public bool JudgePlayStateEnd() {
        // ✅ 判断是否播放完毕
        if (!this.GetModel<IChatModel>().Pause.Value && samplesPlayed >= totalSamplesWritten && totalSamplesWritten > 0)
        {
            totalSamplesWritten = 0;
            samplesPlayed = 0;
            return true;
        } else {
            return false;
        }
    }

    public async UniTask PlayLocalAudio(string path, string text, ChatUI chatUI=null) {
        Debug.Log("PlayLocalAudio: " + text);
        var obj = await this.GetSystem<IAddressableSystem>().LoadAssetAsync<AudioClip>(path);
        if (obj.Status == AsyncOperationStatus.Succeeded) {
            var audioClip = obj.Result;
            var samples = new float[audioClip.samples * audioClip.channels];
            audioClip.GetData(samples, 0);
            PlayAudio(samples);
            if (chatUI != null) {
                chatUI.SetText("[AI]: " + text);
                chatUI.SetTipsText("正在说话中...");
                chatUI.SetAnimator("state", 2);
            } else {
                return;
            }
        } else {
            Debug.LogError($"无法加载音频文件 {path}.wav");
        }
    }

    protected override void OnInit() {
        chatModel = this.GetModel<IChatModel>();
        configModel = this.GetModel<IConfigModel>();

        if (Microphone.devices.Length > 0) {
            MicrophoneDevice = Microphone.devices[0];
            Debug.Log("microphoneDevice: " + MicrophoneDevice);
        } else {
            Debug.LogError("没有检测到麦克风设备！");
        }

        // 初始化音频管理器
        InitializeCodecs();
        // InitStreamingPlayback();
    }
}
