using QFramework;
using UnityEngine;
using UnityEngine.UI;
using Utils;

public class RootPwdUI : Mono<PERSON><PERSON><PERSON><PERSON>, IController {
    public Button closeBtn;
    public InputField inputField;
    public Button confirmBtn;
    public Button cancelBtn;

    private void Start() {
        closeBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Close);
            gameObject.SetActiveFast(false);
        });

        cancelBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            gameObject.SetActiveFast(false);
        });

        confirmBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            if (inputField.text.Length == 4) {
                confirmBtn.interactable = false;
                this.GetModel<IRootModel>().RootPwdInput = inputField.text;
                this.GetModel<IUserModel>().IsSuperuser.Value = true;
                this.GetSystem<IRootPwdSystem>().OpenRoot();
            } else {
                var alertInfo = new WarningAlertInfo("Password Error");
                this.SendCommand(new ShowPageCommand(UIPageType.WarningAlert, UILevelType.Alart, alertInfo));
                ResetUI();
            }
        });

        this.RegisterEvent<RootOpenSuccEvent>(OnRootOpenSucc).UnRegisterWhenGameObjectDestroyed(gameObject);
        this.RegisterEvent<RootOpenFailEvent>(OnRootOpenFail).UnRegisterWhenGameObjectDestroyed(gameObject);
    }

    private void OnEnable() {
        ResetUI();
        // statusUI.gameObject.SetActiveFast(false);
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    private void ResetUI() {
        confirmBtn.interactable = true;
        cancelBtn.interactable = true;
        inputField.text = "";
    }

    private void OnRootOpenSucc(RootOpenSuccEvent e) {
        gameObject.SetActiveFast(false);
        this.SendCommand(new ShowPageCommand(UIPageType.RootUI));
    }

    private void OnRootOpenFail(RootOpenFailEvent e) {
        ResetUI();
    }
}
