using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using QFramework;
using UnityEngine;
using UnityEngine.UI;
using Utils;

public class QuestionnaireUI : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IController {
    public Button closeBtn;
    public Button applyBtn;
    public List<ToggleGroup> items;
    public Toggle toggle; // 是否认真填写

    private void Awake() {
        print("QuestionnaireUI Start");
        closeBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Close);
            gameObject.SetActiveFast(false);
        });
        applyBtn.onClick.AddListener(() => { OnApplyBtnClicked().Forget(); });
    }

    /// <summary>
    ///     重置问卷数据 只重设最后一个问题，改为否
    /// </summary>
    private void Reset() {
        toggle.isOn = true;
    }

    /// <summary>
    /// </summary>
    private void OnEnable() {
        Reset();
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    private async UniTaskVoid OnApplyBtnClicked() {
        this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
        await Upload();
        var alertInfo = new WarningAlertInfo("问卷已上传。");
        this.SendCommand(new ShowPageCommand(UIPageType.WarningAlert, UILevelType.Alart, alertInfo));
        gameObject.SetActiveFast(false);
    }

    private async UniTask Upload() {
        var requsetData = new QuestionnaireResultRequest { result = GetResult() };

        var request = await this.GetSystem<INetworkSystem>().PostJson<object>(
            RequestUrl.questionnaireUrl,
            requsetData,
            this.GetModel<IUserModel>().Token.Value);

        if (request.IsSuccess) Debug.Log("问卷上传成功");
    }

    private string GetResult() {
        List<int> result = new();
        foreach (var item in items) {
            var toggles = item.GetComponentsInChildren<Toggle>();
            for (var i = 0; i < toggles.Length; i++)
                if (toggles[i].isOn) {
                    result.Add(i);
                    break;
                }
        }

        var resultStr = string.Join("", result);
        print("问卷：" + resultStr);
        return resultStr;
    }
}
