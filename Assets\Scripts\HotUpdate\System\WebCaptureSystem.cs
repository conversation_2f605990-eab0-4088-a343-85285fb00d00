﻿using QFramework;
using RenderHeads.Media.AVProMovieCapture;
using UnityEngine;

// using static RenderHeads.Media.AVProMovieCapture.CaptureBase;

// AVPRO_MOVIECAPTURE_WEBCAMTEXTURE_SUPPORT 必须添加到生成设置>玩家设置>其他设置>脚本编译中的脚本定义符号
// 5.0.5 插件不支持VuILan导致Android手机Can't create recorder Error。解决：生成设置>玩家设置>其他设置>渲染>关闭自动图形API，并且使OpenGLES3位于上层


public interface IWebCaptureSystem : ISystem {
    public CaptureFromWebCamTexture CaptureComponent { get; set; }
    public void StartRecord(string OutputFolderPath, string FilenamePrefix);
    public void StopRecord();
}

public class WebCaptureSystem : AbstractSystem, IWebCaptureSystem {
    private bool isRecording;

    private string outputPath; // 视频输出路径 "Capture\\Video";

    public CaptureFromWebCamTexture CaptureComponent { get; set; }

    public void StartRecord(string OutputFolderPath, string FilenamePrefix) {
        if (Application.platform != RuntimePlatform.Android) return;
        Debug.Log("video StartRecord");
        if (!CaptureComponent.WebCamTexture.isPlaying) CaptureComponent.WebCamTexture.Play();
        if (!isRecording && CaptureComponent != null && CaptureComponent.WebCamTexture != null &&
            CaptureComponent.WebCamTexture.isPlaying) {
            CaptureComponent.OutputFolderPath = OutputFolderPath;
            CaptureComponent.FilenamePrefix = FilenamePrefix;
            outputPath = OutputFolderPath;

            CaptureComponent.StartCapture();
            isRecording = true;
            Debug.Log("Recording video started.");
        } else {
            Debug.Log($"{CaptureComponent != null} {CaptureComponent != null}");
            Debug.Log($"{CaptureComponent.WebCamTexture != null} {CaptureComponent.WebCamTexture != null}");
            Debug.Log($"{CaptureComponent.WebCamTexture.isPlaying} {CaptureComponent.WebCamTexture.isPlaying}");
            Debug.LogWarning("Recording could not be started.");
        }
    }

    public void StopRecord() {
        if (Application.platform != RuntimePlatform.Android) return;

        if (isRecording && CaptureComponent != null) {
            CaptureComponent.StopCapture();
            isRecording = false;
            Debug.Log("Recording video stopped:" + CaptureComponent.OutputFolderPath + "/" +
                      CaptureComponent.FilenamePrefix);
        }
    }

    private void OnDestroy() {
        if (CaptureComponent.WebCamTexture != null) CaptureComponent.WebCamTexture.Stop();
        StopRecord();
    }

    protected override void OnInit() {
    }
}
