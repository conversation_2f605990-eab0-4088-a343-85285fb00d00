﻿using QFramework;
using UnityEngine;
using UnityEngine.UI;
using Utils;

public class PauseUI : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IController {
    public Button ResumeBtn;
    public Button QuitBtn;

    public void Start() {
        ResumeBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            gameObject.SetActiveFast(false);

            this.SendCommand(new TaskPauseResumeCommand(false));
        });

        QuitBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Button_Low);
            gameObject.SetActiveFast(false);

            // 打开退出UI的预制体 并将自己传进去
            this.SendCommand(new ShowPageCommand(UIPageType.QuitUI));
        });
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }
}
