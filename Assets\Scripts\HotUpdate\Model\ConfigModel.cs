﻿using System;
using QFramework;
using UnityEngine;

[Serializable]
public class Voice {
    public BindableProperty<float> Rate { get; set; } = new();  // 虚拟人说话语速
    public int MicSilenceThreshold { get; set; } // 用户说完话的时间参数 因为是儿童，而且是ASD患者，所以这个参数可以尽可能长一些为好
    public int MicMaxRecordingSeconds { get; set; } // 麦克风最大录音时长
    public Mode VadMode { get; set; } // VAD检测模式 - 客户端
    public string Gender { get; set; } // 虚拟人性别 girl boy
    public float VadThreshold { get; set; } // 客户端VAD检测阈值
}

[Serializable]
public class Net {
    public int MaxAttempts { get; set; } // 网络出错，最大尝试次数
    public int Timeout { get; set; } // 网络请求超时
}

public interface IConfigModel : IModel {
    public string RootPwd { get; set; } // root密码
    public Net Net { get; set; }
    public Voice Voice { get; set; }
}


public class ConfigModel : AbstractModel, IConfigModel {
    public string RootPwd { get; set; } = string.Empty;
    public Net Net { get; set; } = new();
    public Voice Voice { get; set; } = new();

    protected override void OnInit() {
        Net.MaxAttempts = 3;
        Net.Timeout = 8;

        Voice.Rate.Value = PlayerPrefs.GetFloat(PrefKeys.VoiceServiceRate);
        if (Voice.Rate.Value == 0) Voice.Rate.Value = 0.8f;
        Voice.Rate.Register(v => { PlayerPrefs.SetFloat(PrefKeys.VoiceServiceRate, v); });
    }
}
