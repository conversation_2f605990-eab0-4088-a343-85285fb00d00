﻿using QFramework;

public class ShowPageCommand : AbstractCommand {
    private readonly bool mCloseOther;
    private readonly object mData;
    private readonly UILevelType mLevelType;

    private readonly UIPageType mUIPageType;

    public ShowPageCommand(UIPageType pageType, bool mCloseOther = false, UILevelType mLevelType = UILevelType.UIPage) {
        mUIPageType = pageType;
        this.mCloseOther = mCloseOther;
        this.mLevelType = mLevelType;
        mData = null;
    }

    public ShowPageCommand(UIPageType pageType, UILevelType mLevelType) {
        mUIPageType = pageType;
        mCloseOther = false;
        this.mLevelType = mLevelType;
        mData = null;
    }

    public ShowPageCommand(UIPageType pageType, UILevelType mLevelType, object data) {
        mUIPageType = pageType;
        mCloseOther = false;
        this.mLevelType = mLevelType;
        mData = data;
    }

    protected override void OnExecute() {
        this.SendEvent(new ShowPageEvent(mUIPageType, mCloseOther, mLevelType, mData));
    }
}
