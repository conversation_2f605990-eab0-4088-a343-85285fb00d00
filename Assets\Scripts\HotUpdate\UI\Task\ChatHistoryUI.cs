using System.Collections;
using System.Collections.Generic;
using QFramework;
using UnityEngine;
using UnityEngine.UI;
using Utils;

public class ChatHistoryUI : Mono<PERSON><PERSON><PERSON><PERSON>, IController {
    /// <summary>
    ///     聊天UI窗
    /// </summary>
    [SerializeField] public GameObject m_ChatPanel;

    //聊天文本放置的层
    [SerializeField] public RectTransform m_rootTrans;

    //滚动条
    [SerializeField] public ScrollRect m_ScroTectObject;

    [SerializeField] public Button closeBtn;

    //发送聊天气泡
    [SerializeField] public ChatPrefab m_PostChatPrefab;

    //回复的聊天气泡
    [SerializeField] public ChatPrefab m_RobotChatPrefab;

    //缓存已创建的聊天气泡
    private readonly List<GameObject> m_TempChatBox = new();

    public void Awake() {
        this.RegisterEvent<AvatarTextEvent>(e => OnUpdateUI()).UnRegisterWhenGameObjectDestroyed(gameObject);
        // TODO：收到llm类型消息，这里需要更新显示
        this.RegisterEvent<UserTextEvent>(e => OnUpdateUI()).UnRegisterWhenGameObjectDestroyed(gameObject);
    }

    public void Start() {
        closeBtn.onClick.AddListener(() => {
            this.GetSystem<ISoundSystem>().PlaySound(SoundType.Close);
            m_ChatPanel.SetActiveFast(true);
            gameObject.SetActiveFast(false);
        });
    }


    public void OnEnable() {
        ClearChatBox();
        StartCoroutine(GetHistoryChatInfo());
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    private void OnUpdateUI() {
        ClearChatBox();
        if (gameObject.activeSelf) StartCoroutine(GetHistoryChatInfo());
    }

    //清空已创建的对话框
    private void ClearChatBox() {
        while (m_TempChatBox.Count != 0)
            if (m_TempChatBox[0]) {
                Destroy(m_TempChatBox[0].gameObject);
                m_TempChatBox.RemoveAt(0);
            }

        m_TempChatBox.Clear();
    }

    //获取聊天记录列表
    private IEnumerator GetHistoryChatInfo() {
        var chatHistory = this.GetModel<IChatModel>().m_HistoryDataList;
        yield return new WaitForEndOfFrame();

        foreach (var data in chatHistory) {
            if (data.Role == "user") {
                var sendChat = Instantiate(m_PostChatPrefab, m_rootTrans.transform);
                sendChat.SetText(data.Content);
                m_TempChatBox.Add(sendChat.gameObject);
                continue;
            }

            // 移除初始提示词
            // if (chatHistory[0] == data) continue;

            var reChat = Instantiate(m_RobotChatPrefab, m_rootTrans.transform);
            reChat.SetText(data.Content);
            m_TempChatBox.Add(reChat.gameObject);
        }

        //重新计算容器尺寸
        LayoutRebuilder.ForceRebuildLayoutImmediate(m_rootTrans);
        StartCoroutine(TurnToLastLine());
    }

    private IEnumerator TurnToLastLine() {
        yield return new WaitForEndOfFrame();
        //滚动到最近的消息
        m_ScroTectObject.verticalNormalizedPosition = 0;
    }
}
