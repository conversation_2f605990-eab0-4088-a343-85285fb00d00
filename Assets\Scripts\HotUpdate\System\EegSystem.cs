﻿// using System.Collections;
// using System.Collections.Generic;
// using UnityEngine;
// using QFramework;
// using DTC;
// using Newtonsoft.Json;
// using System;
// using System.IO;
// using Utils;
//
//
// public interface IEegSystem : ISystem {
//
// }
//
// public class EegSystem : AbstractSystem, IEegSystem {
//     public BluetoothManager bluetoothManager;
//     public EnterTechBleManager enterTechBleManager;
//     
//     private bool isRecording = false;
//     private string data;
//     public bool isActive = true;
//     
//     
//     private void OnRefreshFoundDevicesCB()
//     {
//         Debug.Log("OnRefreshFoundDevicesCB");
//
//         // 如果未连接
//         if (!string.IsNullOrEmpty(bluetoothManager.ConnectedDeviceAddress))
//         {
//             string connectedAddress = bluetoothManager.ConnectedDeviceAddress;
//             Debug.Log($"Connected to device: Address = {connectedAddress}");
//
//             // 注册处理原始数据和电池信息的回调
//             enterTechBleManager.OnGetOneSecondRawDataCB += OnGetOneSecondRawDataCB;
//             enterTechBleManager.OnUpdateUpdateBatteryInfoCB += OnUpdateUpdateBatteryInfoCB;
//
//             // 等待握手成功后开始数据采集
//             // StartCoroutine(WaitCollect());
//         }
//     }
//     
//     public IEnumerator WaitCollect()
//     {
//         // 等待握手成功
//         yield return new WaitForSeconds(1.5f);  // 如果没数据，就是这里时间给的不够
//         enterTechBleManager.WriteStartCollectCommand();
//     }
//
//     // 更新电池信息
//     private void OnUpdateUpdateBatteryInfoCB(double voltage, uint batteryLevel)
//     {
//         Debug.Log($"Battery Info: Voltage = {voltage}, Level = {batteryLevel}%");
//     }
//
//     public void OnGetOneSecondRawDataCB(string rawData)
//     {
//         if (!isActive) return;
//         Debug.Log($"rawData = {rawData}");
//         if (isRecording)
//         {
//             data += DateTime.Now.ToString(". HHmmssfff:") + rawData.Clone();
//         }
//     }
//     
//     void OnApplicationQuit()
//     {
// #if !UNITY_EDITOR
//         // 停止数据采集并断开连接
//         enterTechBleManager.WriteStopCollectCommand();
//
//         if (bluetoothManager.ConnectedDeviceAddress != null)
//         {
//             bluetoothManager.DisConnectBle(bluetoothManager.ConnectedDeviceAddress);
//         }
//
//         // 注销回调以避免内存泄漏
//         enterTechBleManager.OnGetOneSecondRawDataCB -= OnGetOneSecondRawDataCB;
//         enterTechBleManager.OnUpdateUpdateBatteryInfoCB -= OnUpdateUpdateBatteryInfoCB;
// #endif
//     }
//     
//     
//     protected override void OnInit() {
// #if !UNITY_EDITOR
//         bluetoothManager = BluetoothManager.Instance;
//         enterTechBleManager = EnterTechBleManager.Instance;
//
//         // 请求权限并开始扫描设备
//         bluetoothManager.CheckPermission();
//         bluetoothManager.OnRefreshFoundDevicesCB = OnRefreshFoundDevicesCB;
//         bluetoothManager.Scan();
//
//         if (bluetoothManager.IsBindedDevice)
//         {
//             string addr = bluetoothManager.GetBindDevice();
//             bluetoothManager.ConnectBle(addr);
//             Debug.Log("Connecting to device: Address = " + addr);
//         }
// #endif
//     }
// }



