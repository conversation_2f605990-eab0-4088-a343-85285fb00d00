﻿using System;
using System.IO;
using ICSharpCode.SharpZipLib.Zip;
using UnityEngine;

public static class ZipUtility {
    /// <summary>
    ///     压缩文件和文件夹
    /// </summary>
    /// <param name="_fileOrDirectoryArray">文件夹路径和文件名</param>
    /// <param name="_outputPathName">压缩后的输出路径文件名</param>
    /// <param name="_password">压缩密码</param>
    /// <param name="_zipCallback">ZipCallback对象，负责回调</param>
    /// <returns></returns>
    public static bool Zip(string[] _fileOrDirectoryArray, string _outputPathName, string _password = null,
        ZipCallback _zipCallback = null) {
        if (null == _fileOrDirectoryArray || string.IsNullOrEmpty(_outputPathName)) {
            if (null != _zipCallback)
                _zipCallback.OnFinished(false);

            return false;
        }

        var zipOutputStream = new ZipOutputStream(File.Create(_outputPathName));
        zipOutputStream.SetLevel(6); // 压缩质量和压缩速度的平衡点
        if (!string.IsNullOrEmpty(_password))
            zipOutputStream.Password = _password;

        for (var index = 0; index < _fileOrDirectoryArray.Length; ++index) {
            var result = false;
            var fileOrDirectory = _fileOrDirectoryArray[index];
            if (Directory.Exists(fileOrDirectory))
                result = ZipDirectory(fileOrDirectory, string.Empty, zipOutputStream, _zipCallback);
            else if (File.Exists(fileOrDirectory))
                result = ZipFile(fileOrDirectory, string.Empty, zipOutputStream, _zipCallback);

            if (!result) {
                if (null != _zipCallback)
                    _zipCallback.OnFinished(false);

                return false;
            }
        }

        zipOutputStream.Finish();
        zipOutputStream.Close();

        if (null != _zipCallback)
            _zipCallback.OnFinished(true);

        return true;
    }

    /// <summary>
    ///     压缩文件
    /// </summary>
    /// <param name="_filePathName">文件路径名</param>
    /// <param name="_parentRelPath">要压缩的文件的父相对文件夹</param>
    /// <param name="_zipOutputStream">压缩输出流</param>
    /// <param name="_zipCallback">ZipCallback对象，负责回调</param>
    /// <returns></returns>
    private static bool ZipFile(string _filePathName, string _parentRelPath, ZipOutputStream _zipOutputStream,
        ZipCallback _zipCallback = null) {
        //Crc32 crc32 = new Crc32();
        ZipEntry entry = null;
        FileStream fileStream = null;
        try {
            var entryName = _parentRelPath + '/' + Path.GetFileName(_filePathName);
            entry = new ZipEntry(entryName);
            entry.DateTime = DateTime.Now;

            if (null != _zipCallback && !_zipCallback.OnPreZip(entry))
                return true; // 过滤

            fileStream = File.OpenRead(_filePathName);
            var buffer = new byte[fileStream.Length];
            fileStream.Read(buffer, 0, buffer.Length);
            fileStream.Close();

            entry.Size = buffer.Length;

            //crc32.Reset();
            //crc32.Update(buffer);
            //entry.Crc = crc32.Value;

            _zipOutputStream.PutNextEntry(entry);
            _zipOutputStream.Write(buffer, 0, buffer.Length);
        } catch (Exception _e) {
            Debug.LogError("[ZipUtility.ZipFile]: " + _e);
            return false;
        } finally {
            if (null != fileStream) {
                fileStream.Close();
                fileStream.Dispose();
            }
        }

        if (null != _zipCallback)
            _zipCallback.OnPostZip(entry);

        return true;
    }

    /// <summary>
    ///     压缩文件夹
    /// </summary>
    /// <param name="_path">要压缩的文件夹</param>
    /// <param name="_parentRelPath">要压缩的文件夹的父相对文件夹</param>
    /// <param name="_zipOutputStream">压缩输出流</param>
    /// <param name="_zipCallback">ZipCallback对象，负责回调</param>
    /// <returns></returns>
    private static bool ZipDirectory(string _path, string _parentRelPath, ZipOutputStream _zipOutputStream,
        ZipCallback _zipCallback = null) {
        ZipEntry entry = null;
        try {
            var entryName = Path.Combine(_parentRelPath, Path.GetFileName(_path) + '/');
            entry = new ZipEntry(entryName);
            entry.DateTime = DateTime.Now;
            entry.Size = 0;

            if (null != _zipCallback && !_zipCallback.OnPreZip(entry))
                return true; // 过滤

            _zipOutputStream.PutNextEntry(entry);
            _zipOutputStream.Flush();

            var files = Directory.GetFiles(_path);
            for (var index = 0; index < files.Length; ++index)
                ZipFile(files[index], Path.Combine(_parentRelPath, Path.GetFileName(_path)), _zipOutputStream,
                    _zipCallback);
        } catch (Exception _e) {
            Debug.LogError("[ZipUtility.ZipDirectory]: " + _e);
            return false;
        }

        var directories = Directory.GetDirectories(_path);
        for (var index = 0; index < directories.Length; ++index)
            if (!ZipDirectory(directories[index], Path.Combine(_parentRelPath, Path.GetFileName(_path)),
                    _zipOutputStream, _zipCallback))
                return false;

        if (null != _zipCallback)
            _zipCallback.OnPostZip(entry);

        return true;
    }

    #region ZipCallback

    public abstract class ZipCallback {
        /// <summary>
        ///     压缩单个文件或文件夹前执行的回调
        /// </summary>
        /// <param name="_entry"></param>
        /// <returns>如果返回true，则压缩文件或文件夹，反之则不压缩文件或文件夹</returns>
        public virtual bool OnPreZip(ZipEntry _entry) {
            return true;
        }

        /// <summary>
        ///     压缩单个文件或文件夹后执行的回调
        /// </summary>
        /// <param name="_entry"></param>
        public virtual void OnPostZip(ZipEntry _entry) {
        }

        /// <summary>
        ///     压缩执行完毕后的回调
        /// </summary>
        /// <param name="_result">true表示压缩成功，false表示压缩失败</param>
        public virtual void OnFinished(bool _result) {
        }
    }

    #endregion
}
