﻿using System.Collections.Generic;
using QFramework;
using UnityEngine;
using UnityEngine.UI;

public class AudioVisual : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IController {
    public List<Transform> cubes;

    private readonly Color _greenColor = new(95 / 255f, 222 / 255f, 50 / 255f, 1f);
    private readonly Color _redColor = new(181 / 255f, 69 / 255f, 69 / 255f, 1f);
    private readonly Color _yellowColor = new(255 / 255f, 255 / 255f, 0 / 255f, 1f);

    private IChatModel m_ChatModel;
    private IVoiceAssistantClientSystem _voiceAssistantClientSystem;

    // Start is called before the first frame update
    private void Awake() {
        _voiceAssistantClientSystem = this.GetSystem<IVoiceAssistantClientSystem>();
        m_ChatModel = this.GetModel<IChatModel>();
        _voiceAssistantClientSystem.OnGetSpectrumData = OnGetSpectrumData;
        for (var i = 0; i < transform.childCount; i++) cubes.Add(transform.GetChild(i));

        this.RegisterEvent<UserSpeakEndEvent>(ResetCubes).UnRegisterWhenGameObjectDestroyed(gameObject);
    }

    private void OnEnable() {
        ResetCubes(new UserSpeakEndEvent());
    }

    public IArchitecture GetArchitecture() {
        return Atis.Interface;
    }

    private void OnGetSpectrumData(bool isListening, bool isSpeaking, float[] volumeData) {
        for (var i = 0; i < cubes.Count; i++) {
            var spectrumAvg = Mathf.Log10(Mathf.Abs(volumeData[i * 4]) + 1);
            var targetHeight = Mathf.Clamp(spectrumAvg * m_ChatModel.AudioVisualStepCount.Value, 0, 5f); // 限制高度范围
            var smoothHeight = Mathf.Lerp(cubes[i].localScale.y, targetHeight, 0.5f); // 平滑
            cubes[i].transform.localScale =
                new Vector3(cubes[i].localScale.x, smoothHeight, cubes[i].localScale.z); //将可视化的物体和音波相关联
            Color color;
            if (isSpeaking) {
                color = _greenColor;
            } else if  (isListening) {
                color = _yellowColor;
            } else {
                color = _redColor;
            }

            cubes[i].GetComponent<Image>().color = color;
        }
    }

    private void ResetCubes(UserSpeakEndEvent e) {
        foreach (var cube in cubes) {
            cube.transform.localScale = new Vector3(cube.localScale.x, 0, cube.localScale.z);
            cube.GetComponent<Image>().color = _redColor;
        }
    }
}
