﻿using QFramework;

public class ApplyAvatarCommand : AbstractCommand {
    private readonly int mCurAid;

    public ApplyAvatarCommand(int curAid) {
        mCurAid = curAid;
    }

    protected override void OnExecute() {
        this.GetModel<IUserModel>().Aid.Value = mCurAid;
        var alertInfo = new WarningAlertInfo("Successfully Set");
        this.SendEvent(new ShowPageEvent(UIPageType.WarningAlert, UILevelType.Alart, alertInfo));
        this.SendEvent<UpdateHomepageUIEvent>();
    }
}


public class BuyAvatarCommand : AbstractCommand {
    private readonly AvatarInfo mAvatarInfo;

    public BuyAvatarCommand(AvatarInfo avatarInfo) {
        mAvatarInfo = avatarInfo;
    }

    protected override async void OnExecute() {
        var requsetData = new BuyAvatarRequest { aid = mAvatarInfo.aid };

        var result = await this.GetSystem<INetworkSystem>().PostJson<BuyAvatarResponse>(
            RequestUrl.buyAvatarUrl,
            requsetData,
            this.GetModel<IUserModel>().Token.Value);

        if (result.IsSuccess) {
            this.GetModel<IUserModel>().Star.Value = result.Data.star;
            mAvatarInfo.isHas = true;
            // this.GetModel<IUserModel>().AvatarNum.Value++; // TODO
            this.SendEvent<UnlockAvatarEvent>();
        }
    }
}

public class UpdataAvatarUICommand : AbstractCommand {
    private readonly int mAid;

    public UpdataAvatarUICommand(int aid) {
        mAid = aid;
    }

    protected override void OnExecute() {
        this.SendEvent(new UpdataAvatarUIEvent(mAid));
    }
}
