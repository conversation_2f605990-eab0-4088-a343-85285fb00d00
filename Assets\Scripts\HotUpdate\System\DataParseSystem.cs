﻿using System;
using System.Collections.Generic;
using System.Text;
using Cysharp.Threading.Tasks;
using Newtonsoft.Json;
using QFramework;
using UnityEngine;
using UnityEngine.ResourceManagement.AsyncOperations;
using Utils;

/// <summary>
///     解析数据格式
/// </summary>
public interface IDataParseSystem : ISystem {
    public UserInfo ParseUserInfo(UserInfo jsonData);
    public UniTask ParseAvatarRes();
    public UniTask ParseCharacterData();
    public UniTask<string> ParseHotFixLogData();
    public void ParseConfigData(ConfigResponse jsonData);
    public void ParseSelfUserInfo(UserInfoResponse jsonData);
    public void ParseTaskData(TaskInfoResponse jsonData);
}

public class DataParseSystem : AbstractSystem, IDataParseSystem {

    // 加载解析头像资源
    public async UniTask ParseAvatarRes() {
        var obj = await this.GetSystem<IAddressableSystem>().LoadAssetAsync<TextAsset>(Util.AvatarJsonPath);
        if (obj.Status == AsyncOperationStatus.Succeeded) {
            var avatarList = JsonConvert.DeserializeObject<List<string>>(obj.Result.ToString());
            var avatarModel = this.GetModel<IAvatarModel>();
            avatarModel.AvatarDic.Clear();
            for (var i = 0; i < avatarList.Count; i++) {
                var info = new AvatarInfo();
                info.aid = i;
                info.name = $"头像 {avatarList[i]}";
                info.isHas = true;
                info.star = 0;
                avatarModel.AvatarDic[info.aid] = info;
            }
        }
    }

    // 解析收到的 config
    public void ParseConfigData(ConfigResponse jsonData) {
        Debug.Log("ParseConfigData");

        var configModel = this.GetModel<IConfigModel>();
        configModel.RootPwd = jsonData.rootPwd;
        configModel.Voice.MicSilenceThreshold = (int)jsonData.micSilenceThreshold; // 2000
        configModel.Voice.MicMaxRecordingSeconds = jsonData.micMaxRecordingSeconds; // 400
        configModel.Net.MaxAttempts = jsonData.netMaxAttempts; // 3
        configModel.Net.Timeout = jsonData.netTimeout; // 8
        configModel.Voice.VadMode = (Mode)jsonData.vadMode; // 3
        configModel.Voice.VadThreshold = jsonData.vadThreshold; // 0.0000005f

        this.SendEvent<InitConfigDataEvent>();
    }

    // 解析用户信息 - 登录
    public void ParseSelfUserInfo(UserInfoResponse jsonData) {
        var userModel = this.GetModel<IUserModel>();
        userModel.UserName.Value = jsonData.username;
        userModel.Name.Value = jsonData.name;
        userModel.Uid.Value = jsonData.uid;
        // userModel.Aid.Value = (int)jsonData["aid"];
        userModel.Star.Value = jsonData.star;
        userModel.Gender.Value = jsonData.gender;
        userModel.Age.Value = jsonData.age;
        userModel.Token.Value = jsonData.token;
        userModel.UserInfo.Value = jsonData.userInfo;
        // this.GetModel<IGameModel>().Token.Value = (string)jsonData["token"];
    }

    // 解析用户信息 - 用户信息页面
    public UserInfo ParseUserInfo(UserInfo jsonData) {
        var userInfo = new UserInfo();
        userInfo.username = jsonData.username;
        userInfo.name = jsonData.name;
        userInfo.uid = jsonData.uid;
        // userInfo.aid = (int)jsonData.aid;
        userInfo.star = jsonData.star;
        userInfo.gender = jsonData.gender;
        userInfo.age = jsonData.age;
        return userInfo;
    }

    // 解析任务信息
    public void ParseTaskData(TaskInfoResponse jsonData) {
        var info = new TaskInfo();
        info.id = jsonData.id;
        info.tid = jsonData.tid;
        info.name = jsonData.name;
        info.day = jsonData.day;
        info.time = jsonData.time;
        info.typeId = (TaskType)jsonData.typeId;
        info.prompt = jsonData.prompt;
        // TODO： 其他语音服务的名称
        this.GetModel<ITaskModel>().taskInfo = info;

        Util.SetThemeDir(info.id + "-" + info.name);
    }

    // 解析虚拟人角色信息： cid name gender
    public async UniTask ParseCharacterData() {
        var obj = await this.GetSystem<IAddressableSystem>().LoadAssetAsync<TextAsset>(Util.CharactersJsonPath);
        if (obj.Status == AsyncOperationStatus.Succeeded) {
            var characterModel = this.GetModel<ICharacterModel>();
            characterModel.CharacterDic.Clear();
            characterModel.CharacterDic =
                JsonConvert.DeserializeObject<Dictionary<string, CharacterInfo>>(obj.Result.ToString());
        }
    }

    public async UniTask<string> ParseHotFixLogData() {
        var obj = await this.GetSystem<IAddressableSystem>().LoadAssetAsync<TextAsset>(Util.HotFixLogJsonPath);
        if (obj.Status == AsyncOperationStatus.Succeeded) {
            var data = JsonConvert.DeserializeObject<Dictionary<string, List<string>>>(obj.Result.ToString());
            // 创建一个StringBuilder来构建富文本字符串
            var richTextBuilder = new StringBuilder();

            // 遍历解析后的数据并构建富文本字符串
            foreach (var entry in data) {
                // 日期使用粗体显示
                richTextBuilder.AppendFormat("<b>{0}</b>\n\n", entry.Key);

                foreach (var item in entry.Value)
                    // 任务列表项前加上短横线
                    richTextBuilder.AppendFormat("{0}\n", item);

                richTextBuilder.AppendLine();
            }

            var rootModel = this.GetModel<IRootModel>();
            rootModel.HotFixLog = richTextBuilder.ToString();
            return richTextBuilder.ToString();
        }

        return "";
    }

    protected override void OnInit() {
    }
}


// 用户信息模型
[Serializable]
public class UserInfoResponse {
    public string username;
    public int uid;
    public string name;
    public bool gender;
    public int age;
    public string token;
    public int star;
    public string userInfo;
}

// 配置模型
[Serializable]
public class ConfigResponse {
    public string rootPwd;
    public float micSilenceThreshold;
    public int micMaxRecordingSeconds;
    public int netMaxAttempts;
    public int netTimeout;
    public int vadMode; // Mode
    public float vadThreshold; // 0.0000005f
}

// 配置模型
[Serializable]
public class BuyAvatarResponse {
    public int star;
}

// 问卷模型
[Serializable]
public class IsQuestionnaireResponse {
    public bool IsQuestionnaire;
}

// 任务信息模型
[Serializable]
public class TaskInfoResponse {
    public int id;
    public int tid;
    public string name;
    public int day;
    public int time;
    public int typeId;
    public string prompt;
    public string gptName;
}

// 任务进度模型
[Serializable]
public class ScheduleResponse {
    public float schedule;
}
