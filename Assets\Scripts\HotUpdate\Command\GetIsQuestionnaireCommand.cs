﻿using QFramework;

public class GetIsQuestionnaireCommand : AbstractCommand {
    // TODO 待重构，EEG等逻辑写在这里不合适
    protected override async void OnExecute() {
        var request = await this.GetSystem<INetworkSystem>().PostJson<IsQuestionnaireResponse>(
            RequestUrl.isQuestionnaireUrl,
            token: this.GetModel<IUserModel>().Token.Value);

        if (request.IsSuccess) {
            if (!request.Data.IsQuestionnaire)
                // 打开问卷页面
                this.SendCommand(new ShowPageCommand(UIPageType.QuestionnaireUI));
            else
                this.SendCommand(new CheckEegStatusCommand(() => {
                    this.SendCommand(
                        new UpdateTaskCommand(() => this.SendCommand(new LoadSceneCommand(ScenePath.Task))));
                }));
        }
    }
}
